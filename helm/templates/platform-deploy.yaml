apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: campfire-platform
    {{- include "campfire-platform.labels" . | nindent 4 }}
  name: campfire-platform
  namespace: {{ .Values.namespace }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "campfire-platform.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        app: {{ .Values.platform.cluster.name }}
        actor-system-name: {{ .Values.platform.cluster.name }}
        service-type: api
        {{- include "campfire-platform.selectorLabels" . | nindent 8 }}
    spec:
      automountServiceAccountToken: true
      serviceAccountName: {{ .Values.serviceAccount.name }}
      enableServiceLinks: false
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: campfire-platform
          image:  "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{.Values.image.pullPolicy}}
          env:
          - name: NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: KUBERNETES_POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: JAVA_OPTS
            value: |
              -Xmx{{ .Values.javaOptions.maxHeapSize }} 
              -Xms{{ .Values.javaOptions.initialHeapSize }}
              -XX:+UseZGC
          - name: AI_WEB3_DSP_REWARD_CREDENTIALS_PK
            valueFrom:
              secretKeyRef:
                name: dsp-reward-secrets
                key: AI_WEB3_DSP_REWARD_CREDENTIALS_PK
          envFrom:
            - configMapRef:
                name: service-config
          command: ["/bin/sh", "-c", "java -javaagent:aspectjweaver.jar -javaagent:kanela-agent.jar $JAVA_OPTS -jar application.jar"]
          readinessProbe:
            httpGet:
              path: /api/ping/ready
              port: http
            periodSeconds: 10
            failureThreshold: 10
            initialDelaySeconds: 20
          livenessProbe:
            httpGet:
              path: /alive
              port: management
            periodSeconds: 10
            failureThreshold: 10
            initialDelaySeconds: 20
          lifecycle:
            preStop:
              exec:
                command: [ "sleep", "20" ]
          ports:
            - name: management
              containerPort: 8558
              protocol: TCP
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              memory: {{ .Values.platform.resource.limits.memory }}
            requests:
              cpu: {{ .Values.platform.resource.requests.cpu }}
              memory: {{ .Values.platform.resource.requests.memory }}
          {{- if eq .Values.cloudProvider "gcp" }}
          volumeMounts:
            - name: tmp-storage
              mountPath: /opt/ai-store
          {{- end }}
          {{- if eq .Values.cloudProvider "aws" }}
          volumeMounts:
            - name: persistent-storage
              mountPath: /opt/ai-store
          {{- end }}
      {{- if eq .Values.cloudProvider "gcp" }}
      volumes:
        - name: tmp-storage
          emptyDir: { }
      {{- end }}
      {{- if eq .Values.cloudProvider "aws" }}
      volumes:
        - name: persistent-storage
          persistentVolumeClaim:
            claimName: efs-claim
      {{- end }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
