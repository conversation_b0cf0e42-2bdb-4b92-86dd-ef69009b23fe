package ai.saharaa.distribution;

import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.COMPLETE_COUNT;
import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.TASK_ID;
import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB_USER.*;
import static ai.saharaa.model.TaskSession.TaskSessionStatus.*;

import ai.saharaa.distribution.Contants.Constants;
import ai.saharaa.mappers.JobTaskMapper;
import ai.saharaa.mappers.JobUserMapper;
import ai.saharaa.mappers.ReviewSessionMapper;
import ai.saharaa.mappers.TaskSessionMapper;
import ai.saharaa.model.JobTask;
import ai.saharaa.model.JobUser;
import ai.saharaa.model.ReviewSession;
import ai.saharaa.model.TaskSession;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.time.Duration;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

@Component
@Slf4j
public class CommonCounterManager implements TaskCounter {

  public final String KEY_TOP = "job_%d";
  public final String KEY_FOR_JOB = "job_%d:counterForSingle:%s";
  public final String KEY_FOR_JOB_USER = "job_%d:counterForSingle:user_%d:%s";
  private final RedisTemplate<String, Object> taskCounter;

  private final JobTaskMapper jobTaskMapper;
  private final TaskSessionMapper taskSessionMapper;
  private final ReviewSessionMapper reviewSessionMapper;
  private final JobUserMapper jobUserMapper;

  public CommonCounterManager(
      TaskSessionMapper taskSessionMapper,
      ReviewSessionMapper reviewSessionMapper,
      JobUserMapper jobUserMapper,
      JobTaskMapper jobTaskMapper,
      @Qualifier("redisTemplateForTaskQueue") RedisTemplate<String, Object> taskCounter) {
    this.taskCounter = taskCounter;
    this.jobUserMapper = jobUserMapper;
    this.taskSessionMapper = taskSessionMapper;
    this.reviewSessionMapper = reviewSessionMapper;
    this.jobTaskMapper = jobTaskMapper;
  }

  // --- Methods that READ data or SET specific values (Generally don't need sync) ---
  // getByKey, getJobCount, hgetJobCount, hgetJobHashSize, hgetAllJobCount etc.
  // setJobCount, hsetJobCount, hsetAllJobCount (Usually setting, not incrementing based on state)
  // removeJobData, cancelRemoveJobData (Manage TTL, maybe less critical for sync)

  @Override
  public Long setJobCount(Long jobId, String subKey, Long value) {
    var key = String.format(KEY_FOR_JOB, jobId, subKey);
    // Consider if setting needs to be transactional too?
    // If this SET must only happen on commit, apply the sync pattern here as well.
    // For now, assuming direct set is okay.
    this.taskCounter.opsForValue().set(key, value);
    return value;
  }

  @Override
  public void resetJobCount(Long jobId, String subKey) {
    var key = String.format(KEY_FOR_JOB, jobId, subKey);
    // Consider if setting needs to be transactional too?
    // If this SET must only happen on commit, apply the sync pattern here as well.
    // For now, assuming direct set is okay.
    this.taskCounter.opsForValue().getAndDelete(key);
  }

  @Override
  public Long setJobUserCount(Long jobId, Long userId, String subKey, Long value) {
    var key = String.format(KEY_FOR_JOB_USER, jobId, userId, subKey);
    // Similar consideration as setJobCount
    this.taskCounter.opsForValue().set(key, value);
    return value;
  }

  @Override
  public void resetJobUserCount(Long jobId, Long userId, String subKey) {
    var key = String.format(KEY_FOR_JOB_USER, jobId, userId, subKey);
    // Similar consideration as setJobCount
    this.taskCounter.opsForValue().getAndDelete(key);
  }

  // --- Methods that INCREMENT/DECREMENT (Need Transaction Sync) ---

  /** Increments a job-level counter in Redis AFTER the surrounding transaction commits. */
  @Override
  public void setJobCountIncrement(Long jobId, String subKey, Long value) {
    final long incrementValue = (value == null) ? 1L : value;
    final String key = String.format(KEY_FOR_JOB, jobId, subKey);

    executeAfterCommit(
        () -> {
          log.debug("Executing Redis increment after commit for key: {}", key);
          if (!this.taskCounter.hasKey(key)) {
            getJobCount(jobId, subKey);
          } else {
            taskCounter.opsForValue().increment(key, incrementValue);
          }
        },
        "Increment Job Count",
        key);
  }

  /** Decrements a job-level counter in Redis AFTER the surrounding transaction commits. */
  @Override
  public void setJobCountDecrease(Long jobId, String subKey, Long value) {
    final long decrementValue = (value == null) ? 1L : value;
    final String key = String.format(KEY_FOR_JOB, jobId, subKey);

    executeAfterCommit(
        () -> {
          log.debug("Executing Redis decrement after commit for key: {}", key);
          if (!this.taskCounter.hasKey(key)) {
            getJobCount(jobId, subKey);
          } else {
            taskCounter.opsForValue().decrement(key, decrementValue);
          }
        },
        "Decrement Job Count",
        key);
  }

  /** Increments a job-user-level counter in Redis AFTER the surrounding transaction commits. */
  @Override
  public void setJobUserCountIncrement(Long jobId, Long userId, String subKey, Long value) {
    final long incrementValue = (value == null) ? 1L : value;
    final String key = String.format(KEY_FOR_JOB_USER, jobId, userId, subKey);

    executeAfterCommit(
        () -> {
          log.debug("Executing Redis increment after commit for key: {}", key);
          if (!this.taskCounter.hasKey(key)) {
            getJobUserCount(jobId, userId, subKey);
          } else {
            taskCounter.opsForValue().increment(key, incrementValue);
          }
        },
        "Increment Job User Count",
        key);
  }

  /** Decrements a job-user-level counter in Redis AFTER the surrounding transaction commits. */
  @Override
  public void setJobUserCountDecrease(Long jobId, Long userId, String subKey, Long value) {
    final long decrementValue = (value == null) ? 1L : value;
    final String key = String.format(KEY_FOR_JOB_USER, jobId, userId, subKey);

    executeAfterCommit(
        () -> {
          log.debug("Executing Redis decrement after commit for key: {}", key);
          if (!this.taskCounter.hasKey(key)) {
            getJobUserCount(jobId, userId, subKey);
          } else {
            taskCounter.opsForValue().decrement(key, decrementValue);
          }
        },
        "Decrement Job User Count",
        key);
  }

  /** Increments a job-level hash counter in Redis AFTER the surrounding transaction commits. */
  @Override
  public void hIncrementJobCount(Long jobId, String subKey, String hashKey, Integer value) {
    final int incrementValue = (value == null) ? 1 : value;
    final String key = String.format(KEY_FOR_JOB, jobId, subKey);

    executeAfterCommit(
        () -> {
          log.debug(
              "Executing Redis hash increment after commit for key: {}, hashKey: {}", key, hashKey);
          taskCounter.opsForHash().increment(key, hashKey, incrementValue);
        },
        "Hash Increment Job Count",
        key + "::" + hashKey); // Include hashKey in description
  }

  /** Decrements a job-level hash counter in Redis AFTER the surrounding transaction commits. */
  @Override
  public void hDecreaseJobCount(Long jobId, String subKey, String hashKey, Integer value) {
    final int decrementValue = (value == null) ? 1 : value;
    final String key = String.format(KEY_FOR_JOB, jobId, subKey);

    executeAfterCommit(
        () -> {
          log.debug(
              "Executing Redis hash decrement after commit for key: {}, hashKey: {}", key, hashKey);
          // Note: Redis HINCRBY is used for both increment and decrement (with negative value)
          taskCounter.opsForHash().increment(key, hashKey, -decrementValue);
        },
        "Hash Decrement Job Count",
        key + "::" + hashKey); // Include hashKey in description
  }

  // --- Helper method for transaction synchronization ---

  /**
   * Executes the given Redis operation only after the current transaction commits successfully. If
   * no transaction is active, executes immediately with a warning.
   *
   * @param redisOperation The Runnable containing the Redis command to execute.
   * @param operationDescription A description for logging purposes.
   * @param redisKey The Redis key involved (for logging).
   */
  private void executeAfterCommit(
      Runnable redisOperation, String operationDescription, String redisKey) {
    if (TransactionSynchronizationManager.isActualTransactionActive()) {

      TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
        @Override
        public void afterCommit() {
          // This code runs ONLY AFTER the DB transaction commits successfully
          try {
            log.debug(
                "Executing Redis operation '{}' for key [{}] after commit.",
                operationDescription,
                redisKey);
            redisOperation.run();
            log.debug(
                "Successfully executed Redis operation '{}' for key [{}] after commit.",
                operationDescription,
                redisKey); // Log success
          } catch (Exception e) {
            // CRITICAL: DB commit succeeded, but Redis failed.
            log.error(
                "CRITICAL: Failed to execute Redis operation '{}' for key [{}] after DB commit. Data inconsistency possible.",
                operationDescription,
                redisKey,
                e);
            // Implement alerting or add to a dead-letter/retry queue here
          }
        }

        @Override
        public void afterCompletion(int status) {
          // This code runs after the transaction completes (commit OR rollback)
          switch (status) {
            case TransactionSynchronization.STATUS_COMMITTED:
              // Action already taken in afterCommit, log if needed
              log.trace(
                  "Transaction completed with status COMMITTED for Redis operation '{}', key [{}].",
                  operationDescription,
                  redisKey);
              break;
            case TransactionSynchronization.STATUS_ROLLED_BACK:
              log.debug(
                  "Redis operation '{}' for key [{}] skipped due to transaction rollback.",
                  operationDescription,
                  redisKey);
              break;
            case TransactionSynchronization.STATUS_UNKNOWN:
              log.warn(
                  "Redis operation '{}' for key [{}] transaction status unknown after completion.",
                  operationDescription,
                  redisKey);
              break;
            default:
              log.warn(
                  "Redis operation '{}' for key [{}] transaction completed with unexpected status: {}",
                  operationDescription,
                  redisKey,
                  status);
          }
        }
      });
      log.debug(
          "Registered Redis operation '{}' for key [{}] to execute after commit.",
          operationDescription,
          redisKey);
    } else {
      try {
        redisOperation.run();
      } catch (Exception e) {
        log.error(
            "Failed to execute Redis operation '{}' for key [{}] (no transaction).",
            operationDescription,
            redisKey,
            e);
        // Handle error appropriately for non-transactional context
      }
    }
  }

  // --- Other existing methods (getters, setters, etc.) ---
  @Override
  public void cancelRemoveJobData(Long jobId) {
    var key = String.format(KEY_TOP, jobId);
    this.taskCounter.opsForValue().getAndPersist(key);
  }

  @Override
  public void removeJobData(Long jobId) {
    var key = String.format(KEY_TOP, jobId);
    this.taskCounter.opsForValue().getAndExpire(key, Duration.ofDays(7));
  }

  @Override
  public Object getByKey(String key) {
    if (this.taskCounter.hasKey(key)) {
      return this.taskCounter.opsForValue().get(key);
    }
    return null;
  }

  @Override
  public Long getJobCount(Long jobId, String subKey) {
    var key = String.format(KEY_FOR_JOB, jobId, subKey);
    if (this.taskCounter.hasKey(key)) {
      return Long.valueOf(this.taskCounter.opsForValue().get(key).toString());
    } else {
      if (subKey.equals(COMPLETE_COUNT.value)) {
        var completeCount = taskSessionMapper.selectCount(new QueryWrapper<TaskSession>()
            .lambda()
            .eq(TaskSession::getJobId, jobId)
            .eq(TaskSession::getDeleted, false)
            .eq(TaskSession::getStatus, PendingSpot));
        this.taskCounter.opsForValue().set(key, completeCount);
        return completeCount;
      } else if (subKey.equals(PENDING_PIPELINE_COUNT.value)) {
        var pendingPipelineCount = taskSessionMapper.selectCount(new QueryWrapper<TaskSession>()
          .lambda()
          .eq(TaskSession::getJobId, jobId)
          .eq(TaskSession::getDeleted, false)
          .eq(TaskSession::getStatus, PendingPipeline));
        this.taskCounter.opsForValue().set(key, pendingPipelineCount);
        return pendingPipelineCount;
      } else if (subKey.equals(TASK_ID.value)) {
        var singleTaskId = jobTaskMapper
            .selectOne(new QueryWrapper<JobTask>()
                .lambda()
                .eq(JobTask::getJobId, jobId)
                .eq(JobTask::getDeleted, false)
                .last("limit 1"))
            .asOpt();
        if (singleTaskId.isPresent()) {
          this.taskCounter.opsForValue().set(key, singleTaskId.get().getTaskId());
          return singleTaskId.get().getTaskId();
        }
      }
    }
    return 0L;
  }

  @Override
  public Long hgetJobCount(Long jobId, String subKey, String hashKey) {
    var key = String.format(KEY_FOR_JOB, jobId, subKey);
    if (this.taskCounter.opsForHash().hasKey(key, hashKey)) {
      return Long.valueOf(this.taskCounter.opsForHash().get(key, hashKey).toString());
    }
    return 0L;
  }

  @Override
  public Long hgetJobHashSize(Long jobId, String subKey) {
    var key = String.format(KEY_FOR_JOB, jobId, subKey);
    return this.taskCounter.opsForHash().size(key);
  }

  @Override
  public void hsetJobCount(Long jobId, String subKey, String hashKey, Long value) {
    var key = String.format(KEY_FOR_JOB, jobId, subKey);
    this.taskCounter.opsForHash().put(key, hashKey, value);
  }

  @Override
  public void hsetAllJobCount(Long jobId, String subKey, Map<String, Long> values) {
    var key = String.format(KEY_FOR_JOB, jobId, subKey);
    this.taskCounter.opsForHash().putAll(key, values);
  }

  @Override
  public Map<Object, Object> hgetAllJobCount(Long jobId, String subKey) {
    var key = String.format(KEY_FOR_JOB, jobId, subKey);
    return this.taskCounter.opsForHash().entries(key);
  }

  @Override
  public Long getJobUserCount(Long jobId, Long userId, String subKey) {
    var key = String.format(KEY_FOR_JOB_USER, jobId, userId, subKey);
    if (this.taskCounter.hasKey(key)) {
      return Long.valueOf(this.taskCounter.opsForValue().get(key).toString());
    } else {
      var ju = jobUserMapper.selectOne(new QueryWrapper<JobUser>()
          .lambda()
          .eq(JobUser::getTaskListSessionId, jobId)
          .eq(JobUser::getUserId, userId)
          .eq(JobUser::getDeleted, false));
      if (ju == null) {
        return 0L;
      }
      var isLabeler = ju.getRole().equals(JobUser.JobUserRole.LABELER);
      var isReviewer = ju.getRole().equals(JobUser.JobUserRole.REVIEWER);
      if (isLabeler) {
        if (SUBMITTED_COUNT.value.equals(subKey)) {
          var count = taskSessionMapper.selectCount(new QueryWrapper<TaskSession>()
              .lambda()
              .eq(TaskSession::getJobId, jobId)
              .eq(TaskSession::getUserId, userId)
              .ne(TaskSession::getStatus, PENDING)
              .eq(TaskSession::getDeleted, false));
          this.taskCounter.opsForValue().set(key, count);
          return count;
        } else if (USER_RIGHT_COUNT.value.equals(subKey)
            || Constants.CACHE_KEY_FOR_JOB_USER.COMPLETE_COUNT.value.equals(subKey)) {
          var count = taskSessionMapper.selectCount(new QueryWrapper<TaskSession>()
              .lambda()
              .eq(TaskSession::getJobId, jobId)
              .eq(TaskSession::getUserId, userId)
              .eq(TaskSession::getStatus, PendingSpot)
              .eq(TaskSession::getDeleted, false));
          this.taskCounter.opsForValue().set(key, count);
          return count;
        }
      } else if (isReviewer) {
        if (SUBMITTED_COUNT.value.equals(subKey)) {
          var count = reviewSessionMapper.selectCount(new QueryWrapper<ReviewSession>()
              .lambda()
              .eq(ReviewSession::getJobId, jobId)
              .eq(ReviewSession::getUserId, userId)
              .ne(ReviewSession::getStatus, ReviewSession.ReviewSessionStatus.PENDING)
              .eq(ReviewSession::getDeleted, false));
          this.taskCounter.opsForValue().set(key, count);
          return count;
        } else if (USER_RIGHT_COUNT.value.equals(subKey)
            || Constants.CACHE_KEY_FOR_JOB_USER.COMPLETE_COUNT.value.equals(subKey)) {
          var count = reviewSessionMapper.selectCount(new QueryWrapper<ReviewSession>()
              .lambda()
              .eq(ReviewSession::getJobId, jobId)
              .eq(ReviewSession::getUserId, userId)
              .eq(ReviewSession::getStatus, ReviewSession.ReviewSessionStatus.SPOTTED)
              .eq(USER_RIGHT_COUNT.value.equals(subKey), ReviewSession::getWrongReview, false)
              .eq(ReviewSession::getDeleted, false));
          this.taskCounter.opsForValue().set(key, count);
          return count;
        }
      }
    }
    return 0L;
  }
}
