package ai.saharaa.dto.reward;

import ai.saharaa.enums.RewardTokenType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MyEarningsDTO {
  // Change to String to support "-" when exchange rate is unavailable
  private String estTotalEarningsSAH;

  private String estTotalEarningsUSD1;

  private String estTotalEarningsUSD;

  private Long approvedDatapoints;
  private List<TokenEarning> tokenEarnings;
  private String nextClaimDate;

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class TokenEarning {
    private String tokenName;
    private Long resourceId;
    private Long tokenId;
    private RewardTokenType rewardTokenType;

    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal historicalTotal;

    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal availableToWithdraw;

    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal freezingTotal;

    // Add this field to control the withdraw button on the frontend
    private String action; // e.g., "WITHDRAW_ENABLED" or "WITHDRAW_DISABLED"
  }
}
