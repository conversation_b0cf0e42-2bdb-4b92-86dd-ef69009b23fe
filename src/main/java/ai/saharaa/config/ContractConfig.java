package ai.saharaa.config;

import ai.saharaa.common.contracts.AchievementOnContractManager;
import ai.saharaa.common.contracts.DSPReward;
import ai.saharaa.common.contracts.LevelAchievementManager;
import java.math.BigInteger;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.web3j.crypto.Credentials;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.methods.response.EthGasPrice;
import org.web3j.protocol.http.HttpService;
import org.web3j.tx.RawTransactionManager;
import org.web3j.tx.TransactionManager;
import org.web3j.tx.gas.ContractGasProvider;
import org.web3j.tx.gas.DefaultGasProvider;

@Configuration
public class ContractConfig {
  private final Logger log = org.slf4j.LoggerFactory.getLogger(ContractConfig.class);

  private final String rpcUrl;
  private final String credentialsPrivateKey;
  private final String achievementManagerContractAddress;
  private final String achievementOnContractAddress;
  private final String dspRewardCredentialsPk;
  private final String dspRewardContractAddress;
  private final Long chainId;

  public ContractConfig(
      @Value("${ai.saharaa.web3.rpc_url}") String rpcUrl,
      @Value("${ai.saharaa.web3.credentials_pk}") String credentialsPrivateKey,
      @Value("${ai.saharaa.web3.achievement_manager_contract_address}")
          String achievementManagerContractAddress,
      @Value("${ai.saharaa.web3.achievement_on_contract_address}")
          String achievementOnContractAddress,
      @Value("${ai.saharaa.web3.dsp_reward_credentials_pk}") String dspRewardCredentialsPk,
      @Value("${ai.saharaa.web3.dsp_reward_contract_address}") String dspRewardContractAddress,
      @Value("${ai.saharaa.web3.dsp_reward_chain_id}") Long chainId) {
    this.rpcUrl = rpcUrl;
    this.credentialsPrivateKey = credentialsPrivateKey;
    this.achievementManagerContractAddress = achievementManagerContractAddress;
    this.achievementOnContractAddress = achievementOnContractAddress;
    this.dspRewardCredentialsPk = dspRewardCredentialsPk;
    this.dspRewardContractAddress = dspRewardContractAddress;
    this.chainId = chainId;
  }

  @Bean
  public Web3j web3j() throws Exception {
    return Web3j.build(new HttpService(rpcUrl));
  }

  @Bean(name = "dspRewardTransactionManager")
  public TransactionManager transactionManager(Web3j web3j) {
    var credentials = Credentials.create(dspRewardCredentialsPk);
    return new RawTransactionManager(web3j, credentials, this.chainId);
  }

  @Bean
  public LevelAchievementManager levelAchievementManager(Web3j web3j) throws Exception {
    var credentials = Credentials.create(credentialsPrivateKey);
    var gasProvider = new DefaultGasProvider();
    var levelAchievementManager = LevelAchievementManager.load(
        achievementManagerContractAddress, web3j, credentials, gasProvider);
    log.info("levelAchievementManager created: {}", levelAchievementManager);
    return levelAchievementManager;
  }

  @Bean
  public AchievementOnContractManager achievementOnContractManager(Web3j web3j) throws Exception {
    var credentials = Credentials.create(credentialsPrivateKey);
    var gasProvider = new DefaultGasProvider();
    var achievementOnContractManager = AchievementOnContractManager.load(
        achievementOnContractAddress, web3j, credentials, gasProvider);
    log.info("achievementOnContractManager created: {}", achievementOnContractManager);
    return achievementOnContractManager;
  }

  @Bean
  public DSPReward dspRewardContract(Web3j web3j, TransactionManager transactionManager) {
    EthGasPrice ethGasPrice = web3j.ethGasPrice().send();
    BigInteger gasPrice =
        ethGasPrice.getGasPrice().multiply(BigInteger.valueOf(2)); //  todo: clarify here
    BigInteger gasLimit = BigInteger.valueOf(12_000_000);
    ContractGasProvider contractGasProvider = new ContractGasProvider() {
      @Override
      public BigInteger getGasPrice(String contractFunc) {
        return gasPrice;
      }

      @Override
      public BigInteger getGasPrice() {
        return gasPrice;
      }

      @Override
      public BigInteger getGasLimit(String contractFunc) {
        return gasLimit;
      }

      @Override
      public BigInteger getGasLimit() {
        return gasLimit;
      }
    };

    var dspRewardContract =
        DSPReward.load(dspRewardContractAddress, web3j, transactionManager, contractGasProvider);
    log.info("DSPReward contract loaded at address: {}", dspRewardContract.getContractAddress());
    return dspRewardContract;
  }
}
