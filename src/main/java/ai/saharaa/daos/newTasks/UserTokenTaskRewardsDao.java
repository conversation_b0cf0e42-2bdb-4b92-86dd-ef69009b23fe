package ai.saharaa.daos.newTasks;

import static ai.saharaa.model.newTasks.BatchRewardRecord.PoolStatus.LAUNCHED_WITH_CD_READY;
import static ai.saharaa.model.newTasks.UserTokenRewardClaims.TokenClaimStatus.PENDING;
import static ai.saharaa.model.newTasks.UserTokenTaskRewards.UserRewardStatus.WITHDRAW_DONE;

import ai.saharaa.dto.reward.EarningBreakdownDTO;
import ai.saharaa.enums.RewardTokenType;
import ai.saharaa.enums.SortType;
import ai.saharaa.mappers.newTasks.UserTokenTaskRewardsMapper;
import ai.saharaa.model.Job;
import ai.saharaa.model.JobUser;
import ai.saharaa.model.newTasks.BatchRewardRecord;
import ai.saharaa.model.newTasks.UserClaimRewardsDTO;
import ai.saharaa.model.newTasks.UserTokenTaskRewards;
import ai.saharaa.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.toolkit.JoinWrappers;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class UserTokenTaskRewardsDao {
  private final UserTokenTaskRewardsMapper userTokenTaskRewardsMapper;

  public UserTokenTaskRewardsDao(UserTokenTaskRewardsMapper userTokenTaskRewardsMapper) {
    this.userTokenTaskRewardsMapper = userTokenTaskRewardsMapper;
  }

  public UserTokenTaskRewards create(UserTokenTaskRewards userTokenTaskRewards) {
    userTokenTaskRewardsMapper.insert(userTokenTaskRewards);
    return userTokenTaskRewards;
  }

  public void updateByArrDataWithId(Collection<UserTokenTaskRewards> updateArr) {
    userTokenTaskRewardsMapper.updateById(updateArr);
  }

  public IPage<EarningBreakdownDTO> getUserTokenTaskRewards(
      Long userId,
      int page,
      int size,
      RewardTokenType coinType,
      String taskName,
      String role,
      SortType sortType,
      Boolean claimed) {
    return userTokenTaskRewardsMapper.selectJoinPage(
        Page.of(page, size),
        EarningBreakdownDTO.class,
        JoinWrappers.lambda(UserTokenTaskRewards.class)
            .leftJoin(Job.class, "j", Job::getId, UserTokenTaskRewards::getJobId)
            .leftJoin(JobUser.class, "ju", ju -> ju.eq(JobUser::getUserId, userId)
                .eq(JobUser::getTaskListSessionId, Job::getId))
            .selectAll(UserTokenTaskRewards.class)
            .select(Job::getName)
            .select(JobUser::getRole)
            .eq(Boolean.TRUE.equals(claimed), UserTokenTaskRewards::getStatus, WITHDRAW_DONE)
            .apply(
                !(taskName == null || taskName.isEmpty()),
                "j.name ilike {0}",
                "%".concat(taskName == null ? "" : taskName).concat("%"))
            .and(
                "annotator".equals(role) || "reviewer".equals(role),
                wrapper -> wrapper.eq(
                    JobUser::getRole,
                    "annotator".equals(role)
                        ? JobUser.JobUserRole.LABELER
                        : JobUser.JobUserRole.REVIEWER))
            .and(
                coinType != null,
                wrapper -> wrapper.eq(UserTokenTaskRewards::getRewardTokenType, coinType))
            .eq(UserTokenTaskRewards::getUserId, userId)
            .ne(UserTokenTaskRewards::getAmount, BigDecimal.ZERO)
            .eq(UserTokenTaskRewards::getDeleted, false)
            .orderBy(
                (sortType == null || SortType.CreatedAtDesc == sortType),
                false,
                UserTokenTaskRewards::getCreatedAt)
            .orderBy(SortType.CreatedAtAsc == sortType, true, UserTokenTaskRewards::getCreatedAt)
            .orderBy(
                SortType.UserTokenRewardDesc == sortType, false, UserTokenTaskRewards::getAmount)
            .orderBy(
                SortType.UserTokenRewardAsc == sortType, true, UserTokenTaskRewards::getAmount));
  }

  public List<Long> getJobIdsByUserId(Long userId) {
    return userTokenTaskRewardsMapper.selectJoinList(
        Long.class,
        JoinWrappers.lambda(UserTokenTaskRewards.class)
            .select(UserTokenTaskRewards::getJobId)
            .eq(UserTokenTaskRewards::getUserId, userId)
            .eq(UserTokenTaskRewards::getDeleted, false)
            .distinct());
  }

  public List<UserTokenTaskRewards> getUserTokenTaskRewards(Long userId) {
    return userTokenTaskRewardsMapper.selectList(new QueryWrapper<UserTokenTaskRewards>()
        .lambda()
        .select(
            UserTokenTaskRewards::getUserId,
            UserTokenTaskRewards::getJobId,
            UserTokenTaskRewards::getThirdPartyTokenId,
            UserTokenTaskRewards::getAmount,
            UserTokenTaskRewards::getStatus,
            UserTokenTaskRewards::getRewardTokenType)
        .eq(UserTokenTaskRewards::getUserId, userId)
        .in(UserTokenTaskRewards::getRewardTokenType, RewardTokenType.USD1, RewardTokenType.SAHARA)
        .ne(UserTokenTaskRewards::getAmount, BigDecimal.ZERO)
        .eq(UserTokenTaskRewards::getDeleted, false));
  }

  public List<UserTokenTaskRewards> getUserTokenTaskRewardsByToken(
      Long userId, RewardTokenType rewardTokenType) {
    return userTokenTaskRewardsMapper.selectList(new QueryWrapper<UserTokenTaskRewards>()
        .lambda()
        .eq(UserTokenTaskRewards::getUserId, userId)
        .eq(UserTokenTaskRewards::getRewardTokenType, rewardTokenType)
        .eq(UserTokenTaskRewards::getDeleted, false));
  }

  public List<UserTokenTaskRewards> getListByUserId(Long userId, List<Long> jobIds) {
    return userTokenTaskRewardsMapper.selectList(new QueryWrapper<UserTokenTaskRewards>()
        .lambda()
        .eq(UserTokenTaskRewards::getUserId, userId)
        .in(UserTokenTaskRewards::getJobId, jobIds)
        .eq(UserTokenTaskRewards::getDeleted, false));
  }

  public UserTokenTaskRewards findRewardByDetails(
      Long userId, Long thirdPartyTokenId, BigDecimal amount) {
    // Find a task reward that has not been claimed yet and matches the details.
    // The logic to check if it's "unclaimed" would need to be more robust, but for now we find the
    // first match.
    return userTokenTaskRewardsMapper.selectOne(
        new QueryWrapper<UserTokenTaskRewards>()
            .lambda()
            .eq(UserTokenTaskRewards::getUserId, userId)
            .eq(UserTokenTaskRewards::getThirdPartyTokenId, thirdPartyTokenId)
            .eq(UserTokenTaskRewards::getAmount, amount)
            .eq(UserTokenTaskRewards::getDeleted, false)
            .last("LIMIT 1") // Return the first one found
        );
  }

  public void updateMerkleProofForUserReward(
      Long userId, Long jobId, Long thirdPartyTokenId, List<String> merkleProof) {
    userTokenTaskRewardsMapper.updateMerkleProofForUserReward(
        userId, jobId, thirdPartyTokenId, merkleProof);
  }

  public List<UserTokenTaskRewards> findMainTokenRewardsByJobId(Long jobId) {
    return userTokenTaskRewardsMapper.selectList(new QueryWrapper<UserTokenTaskRewards>()
        .lambda()
        .select(
            UserTokenTaskRewards::getId,
            UserTokenTaskRewards::getUserId,
            UserTokenTaskRewards::getRewardTokenType,
            UserTokenTaskRewards::getJobId,
            UserTokenTaskRewards::getAmount)
        .eq(UserTokenTaskRewards::getJobId, jobId)
        .ne(UserTokenTaskRewards::getRewardTokenType, RewardTokenType.THIRD_PARTY_TOKEN)
        .eq(UserTokenTaskRewards::getDeleted, false));
  }

  public List<UserTokenTaskRewards> selectReadyClaimRecords(Long userId, Integer tokenType) {
    return userTokenTaskRewardsMapper.selectJoinList(
        UserTokenTaskRewards.class,
        JoinWrappers.lambda(UserTokenTaskRewards.class)
            .selectAll(UserTokenTaskRewards.class)
            .innerJoin(Job.class, Job::getId, UserTokenTaskRewards::getJobId)
            .innerJoin(BatchRewardRecord.class, BatchRewardRecord::getBatchId, Job::getBatchId)
            .eq(UserTokenTaskRewards::getUserId, userId)
            .eq(UserTokenTaskRewards::getRewardTokenType, tokenType)
            .eq(UserTokenTaskRewards::getStatus, PENDING)
            .eq(BatchRewardRecord::getPoolStatus, LAUNCHED_WITH_CD_READY)
            .eq(BatchRewardRecord::getDeleted, false)
            .eq(UserTokenTaskRewards::getDeleted, false));
  }

  public List<UserTokenTaskRewards> updateUserClaiming(
      Long userId, List<UserClaimRewardsDTO> res2) {
    var jobIds = res2.map(UserClaimRewardsDTO::getTaskId).map(Long::valueOf).toList();
    var targets = getListByUserId(userId, jobIds);
    var now = DateUtils.now();
    targets.forEach(target -> {
      target.setStatus(WITHDRAW_DONE);
      target.setUpdatedAt(now);
    });
    userTokenTaskRewardsMapper.updateById(targets);
    return targets;
  }
}
