package ai.saharaa.controllers;

import static ai.saharaa.model.Job.JobType.INDIVIDUAL;
import static ai.saharaa.model.JobUser.JobUserRole.*;
import static ai.saharaa.utils.Constants.CommonErrorContent.BATCH_NOT_FOUND;
import static ai.saharaa.utils.Constants.ROLE_EXTERNAL_USER;
import static ai.saharaa.utils.Constants.ROLE_USER;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

import ai.saharaa.actors.jobs.JobSessionActor;
import ai.saharaa.common.cache.IGlobalCache;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.config.perms.*;
import ai.saharaa.daos.*;
import ai.saharaa.distribution.TaskDistributor;
import ai.saharaa.distribution.TaskVisitorProvider;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.SimpleDataDTO;
import ai.saharaa.dto.batch.BatchDetailsDTO;
import ai.saharaa.dto.batch.BatchSampleDetailsDTO;
import ai.saharaa.dto.job.*;
import ai.saharaa.dto.sign.*;
import ai.saharaa.dto.task.CreateJobByPreTaskDTO;
import ai.saharaa.dto.task.CreateJobDTO;
import ai.saharaa.dto.task.HybridResourceDTO;
import ai.saharaa.dto.task.TaskListDetailsDTO;
import ai.saharaa.enums.AuditSubmissionType;
import ai.saharaa.enums.MarketSortType;
import ai.saharaa.enums.SortType;
import ai.saharaa.enums.UserLevelFilterType;
import ai.saharaa.model.*;
import ai.saharaa.model.achievement.Achievement;
import ai.saharaa.services.*;
import ai.saharaa.services.achievement.AchievementService;
import ai.saharaa.utils.ActorUtils;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Slf4j
@RestController
@RequestMapping("/api/jobs")
public class JobController {
  private final JobService jobService;
  private final AchievementService achievementService;
  private final JobTaskService jobTaskService;
  private final JobUserService jobUserService;
  private final HoneyPotService honeyPotService;
  private final PreTaskService preTaskService;
  private final ExamSessionDao examSessionDao;
  private final BatchDataService batchDataService;
  private final BatchSampleService batchSampleService;
  private final BatchNDADao batchNDADao;
  private final BatchDao batchDao;
  private final ReviewSessionService reviewSessionService;
  private final SpotSessionService spotSessionService;
  private final TaskListDao taskListDao;
  private final TaskSessionService taskSessionService;
  private final TaskService taskService;
  private final BatchService batchService;
  private final NodeService nodeService;
  private final JobSessionService jobSessionService;
  private final UserService userService;
  private final ClusterConfiguration clusterConfiguration;

  private final NotificationService notificationService;
  private final IndividualsService individualsService;
  private final TaskVisitorProvider taskVisitorProvider;

  public JobController(
      JobService jobService,
      AchievementService achievementService,
      JobTaskService jobTaskService,
      TaskSessionDao taskSessionDao,
      JobTaskDao jobTaskDao,
      IGlobalCache redisCache,
      JobUserService jobUserService,
      BatchDataService batchDataService,
      BatchSampleService batchSampleService,
      BatchNDADao batchNDADao,
      ReviewSessionService reviewSessionService,
      TaskListDao taskListDao,
      TaskSessionService taskSessionService,
      TaskService taskService,
      BatchService batchService,
      JobSessionService jobSessionService,
      ClusterConfiguration clusterConfiguration,
      NodeService nodeService,
      UserService userService,
      SpotSessionService spotSessionService,
      NotificationService notificationService,
      PreTaskService preTaskService,
      ExamSessionDao examSessionDao,
      BatchDao batchDao,
      IndividualsService individualsService,
      HoneyPotService honeyPotService,
      TaskVisitorProvider taskVisitorProvider) {
    this.jobService = jobService;
    this.achievementService = achievementService;
    this.jobTaskService = jobTaskService;
    this.jobUserService = jobUserService;
    this.honeyPotService = honeyPotService;
    this.batchDataService = batchDataService;
    this.batchSampleService = batchSampleService;
    this.batchDao = batchDao;
    this.batchNDADao = batchNDADao;
    this.reviewSessionService = reviewSessionService;
    this.taskListDao = taskListDao;
    this.taskSessionService = taskSessionService;
    this.taskService = taskService;
    this.batchService = batchService;
    this.nodeService = nodeService;
    this.jobSessionService = jobSessionService;
    this.clusterConfiguration = clusterConfiguration;
    this.userService = userService;
    this.spotSessionService = spotSessionService;
    this.notificationService = notificationService;
    this.preTaskService = preTaskService;
    this.examSessionDao = examSessionDao;
    this.individualsService = individualsService;
    this.taskVisitorProvider = taskVisitorProvider;
  }

  @PostMapping("/{id}/assign")
  @IsQueueManager
  public ApiResult<Job> assignJob(@PathVariable Long id, @Valid @RequestBody CreateJobDTO data) {

    return jobService.reassignJob(id, data, ControllerUtils.currentUid()).toApiResult();
  }

  @GetMapping("/{id}")
  public ApiResult<Job> getTaskListSessionById(@PathVariable Long id) {
    var taskListSession = jobService
        .getJobById(id)
        .orThrow(() -> ControllerUtils.notFound("Task list session not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(
            () -> ControllerUtils.forbidden("You are not the owner of this task list session"));

    return taskListSession.toApiResult();
  }

  @PostMapping("/createForIndividual/{batchId}")
  public ApiResult<Job> createJobForIndividual(@PathVariable Long batchId) {
    batchService
        .getBatchById(batchId)
        .orThrow(() -> ControllerUtils.notFound(BATCH_NOT_FOUND))
        .filter(b -> b.getUserType().equals(Batch.UserType.EXTERNAL_LABELER))
        .ensurePresent(() -> ControllerUtils.badRequest("invalid id"));
    return jobService.createJobForIndividuals(batchId).toApiResult();
  }

  @PostMapping("/join/{id}/individual")
  public ApiResult<JobUser> joinIndividualJob(@PathVariable Long id) {
    var curId = ControllerUtils.currentUid();
    var ju = jobUserService.getJobUserByJobAndUserId(id, curId);
    if (ju.isPresent()) {
      if (BLOCKED_BY_CUTOFF.equals(ju.get().getRole())) {
        throw ControllerUtils.badRequest(
            "Sorry, there are no remaining roles available for assignment.");
      }
      return ju.get().toApiResult();
    }
    var job = jobService
        .getJobById(id)
        .orThrow(() -> ControllerUtils.notFound(BATCH_NOT_FOUND))
        .filter(j -> j.getJobType().equals(INDIVIDUAL))
        .orElseThrow(() -> ControllerUtils.badRequest("invalid id"));
    var batch = batchService
        .getBatchById(job.getBatchId())
        .orElseThrow(() -> ControllerUtils.badRequest(BATCH_NOT_FOUND));
    if (batch.getRequiredAchievementSymbol() != null
        && !batch.getRequiredAchievementSymbol().isEmpty()) {
      var accessed = achievementService.isOnchainPassed(
          Achievement.Symbol.valueOf(batch.getRequiredAchievementSymbol()), curId);
      if (!accessed) {
        throw ControllerUtils.badRequest("no perm");
      }
    }
    var banned = individualsService.checkUserBannedInPlatform(curId);
    if (banned) {
      throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(curId));
    }
    userService
        .getUserById(curId)
        .filter(u -> u.getRole() == ROLE_EXTERNAL_USER)
        .ensurePresent(() -> ControllerUtils.badRequest("invalid id"));
    return jobService.joinIndividualJob(job, curId).toApiResult();
  }

  @GetMapping("/{id}/statistic")
  public ApiResult<JobStatisticDTO> getJobStatistic(@PathVariable Long id) {
    var res = jobService.getJobStatistic(id, ControllerUtils.currentUid());
    userService.getUserById(ControllerUtils.currentUid()).ifPresent(u -> {
      if (u.getRole() == ROLE_USER || u.getRole() == ROLE_EXTERNAL_USER) {
        jobUserService
            .getJobUserByJobAndUserId(id, ControllerUtils.currentUid())
            .ifPresent(jobUser -> {
              res.setJobUser(jobUser);
              if (Arrays.asList(POST_EXAM, WAITING_AM_PASSED).contains(jobUser.getRole())) {
                var canAutoGrade =
                    preTaskService.autoGrade(jobService.getJobById(id).get().getBatchId());
                if (canAutoGrade) {
                  var userExamSessions =
                      examSessionDao.listExamSessionsByJobUserId(jobUser.getId());
                  res.setUserExamCount(userExamSessions.size());
                  res.setUserPassExamCount(userExamSessions
                      .filter(ues -> ues.getGrade().doubleValue() > 0)
                      .toList()
                      .size());
                }
              }
            });
      }
    });
    return res.toApiResult();
  }

  @GetMapping("/{id}/statistic/annotator")
  @IsLabeler
  public ApiResult<JobAnnotatorStatisticDTO> getJobStatisticForAnnotator(@PathVariable Long id) {
    return jobService
        .getJobStatisticForAnnotator(id, ControllerUtils.currentUid())
        .toApiResult();
  }

  @GetMapping("/jobs")
  @CheckPageParam()
  public ApiResult<PageResult<UserJobDTO>> getJobByPaginationForUsers(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam(required = false) String name,
      @RequestParam(defaultValue = "") List<Batch.CourseDifficulty> difficulties,
      @RequestParam(defaultValue = "") JobUser.JobUserType status,
      @RequestParam(defaultValue = "normal") Job.JobType jobType,
      @RequestParam(defaultValue = "createdAtDesc") SortType sortType,
      @RequestParam(defaultValue = "") List<JobUser.JobUserRole> role,
      @RequestParam(defaultValue = "") List<String> knowledgeList,
      @RequestParam(defaultValue = "") List<Batch.BatchLabelType> labelType,
      @RequestParam(required = false) BatchSetting.AnnotationMethod annotationMethod,
      @RequestParam(required = false) UserLevelFilterType userLevelFilter) {
    var curId = ControllerUtils.currentUid();
    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ip = Web2AuthService.getClientIP(request);
    return jobService
        .getJobByPagination(
            curId,
            page,
            limit,
            status,
            role,
            name,
            sortType,
            labelType,
            jobType,
            difficulties,
            knowledgeList,
            ip,
            annotationMethod,
            userLevelFilter)
        .toPageResult()
        .toApiResult();
  }

  @GetMapping("/market/individuals")
  @CheckPageParam()
  public ApiResult<PageResult<MarketJobDTO>> getMarketByPaginationForIndividuals(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam(required = false) String name,
      @RequestParam(defaultValue = "") List<Batch.CourseDifficulty> difficulties,
      @RequestParam(defaultValue = "oldest") MarketSortType sortType,
      @RequestParam(defaultValue = "") List<String> knowledgeList,
      @RequestParam(defaultValue = "") List<Batch.BatchLabelType> labelType,
      @RequestParam(defaultValue = "") BatchSetting.AnnotationMethod annotationMethod,
      @RequestParam(defaultValue = "") UserLevelFilterType userLevelFilter,
      @RequestParam(defaultValue = "false") Boolean onlyStakeReq) {
    var curId = ControllerUtils.currentUid();
    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ip = Web2AuthService.getClientIP(request);
    return jobService
        .getMarketByPaginationForIndividuals(
            curId,
            page,
            limit,
            name,
            sortType,
            labelType,
            difficulties,
            knowledgeList,
            ip,
            annotationMethod,
            userLevelFilter,
            onlyStakeReq)
        .toPageResult()
        .toApiResult();
  }

  @GetMapping("/market/specialTaskList")
  public ApiResult<List<MarketJobDTO>> getSpecialTaskList(
      @RequestParam(defaultValue = "") String symbol) {
    var curId = ControllerUtils.currentUid();
    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ip = Web2AuthService.getClientIP(request);
    return jobService.getSpecialTaskList(curId, symbol, ip).toApiResult();
  }

  @GetMapping("/market/specialJobs/joined")
  public ApiResult<List<UserJobDTO>> getSpecialJobs(
      @RequestParam(defaultValue = "") String symbol) {
    var curId = ControllerUtils.currentUid();
    var onChainPassed =
        achievementService.isOnchainPassed(Achievement.Symbol.valueOf(symbol), curId);
    if (!Boolean.TRUE.equals(onChainPassed)) {
      return Collections.<UserJobDTO>emptyList().toApiResult();
    }
    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ip = Web2AuthService.getClientIP(request);
    return jobService.getJoinedSpecialTaskList(symbol, curId, ip).toApiResult();
  }

  @GetMapping("/{id}/individual")
  @IsLabeler
  public ApiResult<MarketJobDTO> getJobForIndividuals(
      @PathVariable Long id, @RequestParam(defaultValue = "detail") String type) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));
    var curId = ControllerUtils.currentUid();
    var ju = jobUserService.getJobUserByJobAndUserId(id, curId);
    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ip = Web2AuthService.getClientIP(request);
    if (ju.isPresent()) {
      if (job.getStatus().equals(Job.JobStatus.FINISHED)) {
        if (!Objects.equals(type, "detail")) {
          return jobService.getJobDetailForFinished(id, curId, job, ju.get()).toApiResult();
        }
        return jobService.getJobForIndividuals(id, curId, job, ju.get(), ip).toApiResult();
      } else {
        return jobService.getJobForIndividuals(id, curId, job, ju.get(), ip).toApiResult();
      }
    } else {
      return jobService.getJobDetailForPublish(id, curId, job, ip).toApiResult();
    }
  }

  @PutMapping("/{id}/skip-notice/individual")
  public ApiResult<Boolean> skipNotice(@PathVariable Long id) {
    jobUserService.skipNotice(id, ControllerUtils.currentUid());
    return ApiResult.success(Boolean.TRUE);
  }

  @GetMapping("/all-jobs")
  @CheckPageParam()
  public ApiResult<PageResult<UserJobDTO>> getJobByPaginationForUsers(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    PageResult<UserJobDTO> res = jobService.getAllJobByPagination(page, limit).toPageResult();
    return ApiResult.success(res);
  }

  @GetMapping("/invitations")
  @CheckPageParam()
  public ApiResult<PageResult<JobInvitationDTO>> getJobInvitations(
      @RequestParam(required = false) Long nodeId,
      @RequestParam(required = false, defaultValue = "1") Integer page,
      @RequestParam(required = false, defaultValue = "10") Integer limit,
      @RequestParam(required = false) String type) {
    return jobService
        .getJobInvitations(nodeId, page, limit, type, ControllerUtils.currentUid())
        .toPageResult()
        .toApiResult();
  }

  @PutMapping("/invitation")
  public ApiResult<JobInvitationDTO> createInvitation(@RequestBody CreateInvitationDTO data) {
    return jobService.createJobInvitation(data, ControllerUtils.currentUid()).toApiResult();
  }

  @PostMapping("/invitations/{inviteId}")
  public ApiResult<Boolean> dealWithJobInvitation(
      @RequestBody InvitationTypeDTO dto, @PathVariable Long inviteId) {
    return jobService
        .dealWithJobInvitation(ControllerUtils.currentUid(), inviteId, dto.getType())
        .toApiResult();
  }

  // TODO: move to actor
  @DeleteMapping("/{id}")
  public void deleteById(@PathVariable Long id) {
    jobService
        .getJobById(id)
        .orThrow(() -> ControllerUtils.notFound("Task list session not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(
            () -> ControllerUtils.forbidden("You are not the owner of this task list session"));

    jobService.deleteJobById(id);
  }

  @GetMapping("/{id}/by-node-manager")
  public ApiResult<Job> getJobDetail(@PathVariable Long id) {
    Optional<Job> job = jobService.getJobById(id);
    if (job.isPresent()) {
      return job.get().toApiResult();
    }
    return null;
  }

  @GetMapping("/for-node-manager")
  @CheckPageParam()
  public ApiResult<PageResult<Job>> getJobByPagination(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam(defaultValue = "deadLineDesc") SortType orderType,
      @RequestParam(required = false) String name) {
    return jobService
        .getJobByPaginationByOwner(
            page,
            limit,
            orderType,
            name,
            Arrays.asList(
                Job.JobStatus.WORKING,
                Job.JobStatus.PAUSING,
                Job.JobStatus.AUDITING,
                Job.JobStatus.AM_AUDIT,
                Job.JobStatus.COMMITTED,
                Job.JobStatus.SETTLING,
                Job.JobStatus.DROPPED,
                Job.JobStatus.FINISHED),
            ControllerUtils.currentUid())
        .toPageResult()
        .toApiResult();
  }

  @GetMapping("/{id}/batch-samples")
  public ApiResult<List<BatchSampleDetailsDTO>> getSamples(@PathVariable Long id) {
    var job = jobService.validateJobPermission(
        id, ControllerUtils.currentUid(), ControllerUtils.isAccountManager());

    var batch = batchService
        .getBatchById(job.getBatchId())
        .orElseThrow(() -> ControllerUtils.notFound(BATCH_NOT_FOUND));
    return batchDataService.getBatchSampleDetails(batch).toApiResult();
  }

  @GetMapping("/{id}/labeling-tasks")
  public ApiResult<List<TaskListDetailsDTO>> getLabelingTasksByJobId(@PathVariable Long id) {
    var job = jobService.validateJobPermission(
        id, ControllerUtils.currentUid(), ControllerUtils.isAccountManager());
    return taskService
        .getTaskListDetails(job.getBatchId(), TaskList.TaskListType.LABEL, true)
        .toApiResult();
  }

  @GetMapping("/{id}/good-examples")
  public ApiResult<List<TaskListDetailsDTO>> getGoodExampleTasks(@PathVariable Long id) {
    var job = jobService.validateJobPermission(
        id, ControllerUtils.currentUid(), ControllerUtils.isAccountManager());
    return taskService
        .getTaskListDetails(job.getBatchId(), TaskList.TaskListType.GOOD_EXAMPLE, true)
        .toApiResult();
  }

  @GetMapping("/{id}/bad-examples")
  public ApiResult<List<TaskListDetailsDTO>> getBadExampleTasks(@PathVariable Long id) {
    var job = jobService.validateJobPermission(
        id, ControllerUtils.currentUid(), ControllerUtils.isAccountManager());
    return taskService
        .getTaskListDetails(job.getBatchId(), TaskList.TaskListType.BAD_EXAMPLE, true)
        .toApiResult();
  }

  @GetMapping("/{id}/exams")
  public ApiResult<List<TaskListDetailsDTO>> getExams(@PathVariable Long id) {
    var job = jobService.validateJobPermission(
        id, ControllerUtils.currentUid(), ControllerUtils.isAccountManager());
    return taskService
        .getTaskListDetails(job.getBatchId(), TaskList.TaskListType.EXAM, true)
        .toApiResult();
  }

  @GetMapping("/{id}/batch")
  public ApiResult<BatchDetailsDTO> getBatchDetailsForJob(@PathVariable Long id) {
    var job = jobService.validateJobPermission(
        id, ControllerUtils.currentUid(), ControllerUtils.isAccountManager());
    var batch = batchService
        .getBatchById(job.getBatchId())
        .filter(v -> !v.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound(BATCH_NOT_FOUND));
    var batchSetting = batchService.getBatchSettingByBatchId(batch.getId());

    var samples = batchSampleService.listBatchSamplesByBatch(batch);
    var r = BatchDetailsDTO.builder()
        .batch(batch)
        .batchSetting(batchSetting)
        .samples(samples)
        .build();

    return r.toApiResult();
  }

  @GetMapping("/{id}/take-job")
  @IsLabeler
  public ApiResult<Optional<TakeJobResultDTO>> takeJob(@PathVariable Long id) {
    // var job = jobService.validateJobPermission(id);
    CompletableFuture<SimpleResult<TaskSession>> r = ActorUtils.askWithDefault(
        this.clusterConfiguration.getJobSessionActor(),
        new JobSessionActor.OpGetTask(id, ControllerUtils.currentUid()));
    var result = r.get();
    var res = result.asApiResult().map(ts -> Optional.ofNullable(ts)
        .map(session -> TakeJobResultDTO.builder()
            .taskSession(session)
            .resource(taskService.getResourceByTaskId(session.getTaskId()).orElse(null))
            .build()));
    // when is hybrid task, return hybrid datas
    res.getData()
        .ifPresent(resData -> Optional.ofNullable(result.getResult()).ifPresent(tsExist -> {
          var taskOption = taskService.getTaskById(tsExist.getTaskId());
          taskOption.ifPresent(task -> {
            if (task.getIsHybrid()) {
              var hybridResources = taskService.getHybridTaskResource(task.getId());
              hybridResources.ifPresent(resData::setHybridTaskResources);
            }
          });
        }));
    return res;
  }

  @GetMapping("/{id}/take-job/for-individuals")
  @IsLabeler
  public ApiResult<Optional<List<TakeJobResultDTO>>> takeJob2(@PathVariable Long id) {

    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ip = Web2AuthService.getClientIP(request);

    var curId = ControllerUtils.currentUid();

    if (individualsService.checkUserBannedInPlatform(curId))
      throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(curId));

    Job job = jobService
        .getJobById(id)
        .orThrow(() -> ControllerUtils.notFound("Task not found."))
        .filter(jb -> jb.getStatus().equals(Job.JobStatus.WORKING)
            || jb.getStatus().equals(Job.JobStatus.COMMITTED)
            || jb.getStatus().equals(Job.JobStatus.PAUSING))
        .orElseThrow(() ->
            ControllerUtils.notFound("This task has ended. Thank you for your participation."));
    var batch = batchService
        .getBatchById(job.getBatchId())
        .orElseThrow(() -> ControllerUtils.notFound(BATCH_NOT_FOUND));
    if (DateUtils.now().after(batch.getDeadline())) {
      throw ControllerUtils.notFound("This task has ended. Thank you for your participation.");
    }

    var jobUser = jobUserService
        .getJobUserByJobAndUserId(id, curId)
        .filter(ju -> ju.getActive().equals(true))
        .orElseThrow(() -> ControllerUtils.badRequest(
            "You've been banned from this task because we've detected malicious activity."));
    BatchSetting batchSetting = batchDao.getBatchSettingById(batch.getId());
    if (batchSetting.getCutoffTime() != null
        && DateUtils.now().after(batchSetting.getCutoffTime())) {
      return takeTaskSessionResult(
          new SimpleResult(new ArrayList<TaskSession>()), batch, batchSetting, jobUser);
    }
    if (job.getStatus().equals(Job.JobStatus.PAUSING)) {
      throw ControllerUtils.badRequest(
          "The task has been paused. We'll notify you when the task resumes.");
    }
    TaskDistributor<TaskSession> taskDistributor = this.taskVisitorProvider
        .getTaskVisitor(batchSetting.getDistributeType(), jobUser.getRole())
        .taskDistributor();
    var isTester = ControllerUtils.isTesterUser();

    List<TaskSession> taskSessions =
        taskDistributor.assignTasks(job, batch, batchSetting, jobUser, ip, isTester);

    return takeTaskSessionResult(new SimpleResult(taskSessions), batch, batchSetting, jobUser);
  }

  private ApiResult<Optional<List<TakeJobResultDTO>>> takeTaskSessionResult(
      SimpleResult<List<TaskSession>> result, Batch b, BatchSetting batchSetting, JobUser jobUser) {
    var res = result.asApiResult().map(ts -> Optional.ofNullable(ts).map(session -> session.stream()
        .map(s -> TakeJobResultDTO.builder()
            .taskSession(s)
            .resource(taskService.getResourceByTaskId(s.getTaskId()).orElse(null))
            .build())
        .toList()));
    // when is hybrid task, return hybrid datas, hybrid function disabled for now.
    //    res.getData()
    //            .ifPresent(resData -> resData.forEach(resDat -> {
    //              var taskOption = taskService.getTaskById(resDat.getTaskSession().getTaskId());
    //              taskOption.ifPresent(task -> {
    //                if (task.getIsHybrid()) {
    //                  var hybridResources = taskService.getHybridTaskResource(task.getId());
    //                  hybridResources.ifPresent(resDat::setHybridTaskResources);
    //                }
    //              });
    //            }));
    res.getData().ifPresent(taskSessionResults -> {
      if (!taskSessionResults.isEmpty()) {
        var hpMode = batchSetting.getHoneyPotSettingForLabeler();
        if (batchSetting.getDistributeType().equals(BatchSetting.DistributeType.RAW)
            && hpMode == BatchSetting.HpSettingType.DP_BOUND) {
          honeyPotService.adaptHoneyPotSessionDpBounded(taskSessionResults, b, jobUser);
        } else if (hpMode == BatchSetting.HpSettingType.NORMAL
            && CollectionUtils.isNotEmpty(batchSetting.getHoneyPotBatches())) {
          honeyPotService.adaptHoneyPotSession(taskSessionResults, b, jobUser, false);
        }
      }
    });
    return res;
  }

  @GetMapping("/{id}/pinnedTaskList") // not in use for now.
  public ApiResult<Optional<List<TakeJobResultDTO>>> getPinnedTaskSessionList(
      @PathVariable Long id) {
    var curId = ControllerUtils.currentUid();
    var j = jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("Job not found"));
    var b = batchService
        .getBatchById(j.getBatchId())
        .orElseThrow(() -> ControllerUtils.notFound(BATCH_NOT_FOUND));
    var taskSessionList = taskSessionService.getPinnedTaskSessionList(id, curId);
    var res = Optional.ofNullable(taskSessionList)
        .map(session -> session.stream()
            .map(s -> TakeJobResultDTO.builder()
                .taskSession(s)
                .resource(taskService.getResourceByTaskId(s.getTaskId()).orElse(null))
                .build())
            .toList())
        .toApiResult();
    res.getData()
        .ifPresent(resData -> resData.forEach(resDat -> {
          var taskOption = taskService.getTaskById(resDat.getTaskSession().getTaskId());
          taskOption.ifPresent(task -> {
            if (task.getIsHybrid()) {
              var hybridResources = taskService.getHybridTaskResource(task.getId());
              hybridResources.ifPresent(resDat::setHybridTaskResources);
            }
          });
        }));
    var batchSetting = batchDao.getBatchSettingById(b.getId());
    if (batchSetting.getHoneyPotSettingForLabeler().equals(BatchSetting.HpSettingType.NORMAL)
        && CollectionUtils.isNotEmpty(batchSetting.getHoneyPotBatches())) {
      var jobUser = jobUserService.getJobUserByJobAndUserId(id, curId);
      res.getData().ifPresent(taskSessionResults -> {
        if (!taskSessionResults.isEmpty()) {
          honeyPotService.adaptHoneyPotSession(taskSessionResults, b, jobUser.get(), true);
        }
      });
    }
    return res;
  }

  @PostMapping("/{id}/report")
  public ApiResult<Boolean> reportTask(
      @PathVariable Long id, @RequestBody ReportTaskInJobDTO body) {
    var curId = ControllerUtils.currentUid();
    var session = taskSessionService
        .getTaskSessionById(body.getSessionId())
        .orThrow(() -> ControllerUtils.notFound("wrong session id"))
        .filter(v -> !v.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("wrong task session"));
    var jobUser = jobUserService
        .getJobUserByJobAndUserId(id, curId)
        .orElseThrow(() -> ControllerUtils.forbidden("no permission"));
    var batchId = jobService.getJobById(id).get().getBatchId();
    var batchSetting = batchDao.getBatchSettingById(batchId);
    if (batchSetting.getDistributeType().equals(BatchSetting.DistributeType.SINGLE)) {
      return jobService.reportTask(id, curId, body).toApiResult();
    }
    if (jobUser.getRole().equals(JobUser.JobUserRole.LABELER)) {
      if (!session.getJobUserId().equals(jobUser.getId())) {
        throw ControllerUtils.badRequest("invalid data");
      }
      var submittedCount = taskSessionService.getSubmittedCount(session.getJobId(), curId);
      var revisedByMajorityVoteCount =
          taskSessionService.countUserRejectedByMajorityVoteSessionsInJob(id, curId);
      var skipQuota = Math.floor(submittedCount / 50f) * 5 + revisedByMajorityVoteCount + 5;
      if (skipQuota > 0) {
        var skippedCount = taskSessionService.getSkippedCount(session.getJobId(), curId);
        if (skippedCount >= skipQuota) {
          throw ControllerUtils.badRequest("you can not report more");
        }
      } else {
        throw ControllerUtils.badRequest("you have no report quota, please do more annotation");
      }
    } else if (jobUser.getRole().equals(JobUser.JobUserRole.REVIEWER)) {
      var myReviewSession = reviewSessionService
          .getMyReviewSessionByTaskSessionId(session.getId(), curId)
          .asOpt();
      if (myReviewSession.isEmpty()) {
        throw ControllerUtils.badRequest("invalid data");
      }
      if (!myReviewSession.get().getJobUserId().equals(jobUser.getId())) {
        throw ControllerUtils.badRequest("invalid data");
      }
      var submittedCount = reviewSessionService.countUserSessionInJob(session.getJobId(), curId);
      var skipQuota = Math.floor(submittedCount / 50f) * 5 + 5;
      if (skipQuota > 0) {
        var skippedCount = taskSessionService.getSkippedCount(session.getJobId(), curId);
        if (skippedCount >= skipQuota) {
          throw ControllerUtils.badRequest("you can not report more");
        }
      } else {
        throw ControllerUtils.badRequest("you have no report quota, please do more review");
      }
    } else {
      throw ControllerUtils.badRequest("wrong job user role");
    }

    if (batchSetting.getDistributeType() == BatchSetting.DistributeType.RAW) {
      taskSessionService.skipTaskSession(session, curId);
    }
    //    if (jobUser.getRole().equals(JobUser.JobUserRole.LABELER)) {
    //
    //      HttpServletRequest request =
    //          ((ServletRequestAttributes)
    // RequestContextHolder.getRequestAttributes()).getRequest();
    //      var ip = Web2AuthService.getClientIP(request);
    //      var workloadRec = workloadLimitService.getJobUserWorkloadType(jobUser, LABELER, ip);
    //      if (WorkloadType.NO_WORKLOAD.equals(workloadRec)) {
    //        taskSessionService.generateNewTaskSessionIfNecessaryCauseOfHoneyPot(session, jobUser);
    //      }
    //    }
    return jobService.reportTask(id, curId, body).toApiResult();
  }

  @GetMapping("/reportList")
  @CheckPageParam()
  public ApiResult<PageResult<JobTaskReportItemDTO>> reportList(
      @RequestParam(required = false) Long jobId,
      @RequestParam(required = false) Integer status,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    // TODO: auth control
    return jobService
        .getReportTaskList(page, limit, jobId, status)
        .toPageResult()
        .toApiResult();
  }

  @GetMapping("/{id}/has-pending-jobs")
  @IsLabeler
  public ApiResult<Boolean> hasPendingJobs(@PathVariable Long id) {
    var role = LABELER;
    var jobUser = jobUserService.getJobUserByJobAndUserId(id, ControllerUtils.currentUid());
    if (jobUser.isEmpty()) {
      return ApiResult.success(false);
    }
    var task = jobTaskService.getOngoingTaskSessionForJobUser(jobUser.get(), role);
    if (task.isPresent()) {
      return ApiResult.success(true);
    }
    return ApiResult.success(
        jobTaskService.hasPendingJobForJobUser(id, jobUser.get().getId()));
  }

  @GetMapping("/{id}/take-review-job")
  public ApiResult<Optional<TakeJobResultDTO>> takeReviewJob(@PathVariable Long id) {
    CompletableFuture<SimpleResult<ReviewSession>> r = ActorUtils.askWithDefault(
        this.clusterConfiguration.getJobSessionActor(),
        new JobSessionActor.OpGetReviewTask(id, ControllerUtils.currentUid()));
    var result = r.get();
    var res = result.asApiResult().map(ts -> Optional.ofNullable(ts)
        .map(session -> TakeJobResultDTO.builder()
            .taskSession(taskSessionService
                .getTaskSessionById(session.getTaskSessionId())
                .orElse(null))
            .reviewSession(session)
            .resource(taskService.getResourceByTaskId(session.getTaskId()).orElse(null))
            .build()));
    res.getData()
        .ifPresent(resData -> Optional.ofNullable(result.getResult()).ifPresent(tsExist -> {
          var taskOption = taskService.getTaskById(tsExist.getTaskId());
          taskOption.ifPresent(task -> {
            if (task.getIsHybrid()) {
              var hybridResources = taskService.getHybridTaskResource(task.getId());
              hybridResources.ifPresent(resData::setHybridTaskResources);
            }
          });
        }));
    return res;
  }

  @GetMapping("/{id}/take-review-jobs/for-individuals")
  public ApiResult<Optional<List<TakeJobResultDTO>>> takeReviewJobs(@PathVariable Long id) {
    var curId = ControllerUtils.currentUid();
    var isTester = ControllerUtils.isTesterUser();
    if (individualsService.checkUserBannedInPlatform(curId)) {
      throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(curId));
    }
    var job = jobService
        .getJobById(id)
        .filter(j -> j.getStatus().equals(Job.JobStatus.WORKING)
            || j.getStatus().equals(Job.JobStatus.COMMITTED)
            || j.getStatus().equals(Job.JobStatus.PAUSING))
        .orElseThrow(() ->
            ControllerUtils.notFound("This task has ended. Thank you for your participation."));
    var batch = batchService
        .getBatchById(job.getBatchId())
        .orElseThrow(() -> ControllerUtils.notFound(BATCH_NOT_FOUND));
    var batchSetting = batchDao.getBatchSettingById(batch.getId());
    var jobUser = jobUserService
        .getJobUserByJobAndUserId(id, curId)
        .filter(ju -> ju.getActive().equals(true))
        .orElseThrow(() -> ControllerUtils.badRequest(
            "You've been banned from this task because we've detected malicious activity."));

    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ip = Web2AuthService.getClientIP(request);
    if (job.getStatus().equals(Job.JobStatus.PAUSING)) {
      throw ControllerUtils.badRequest(
          "The task has been paused. We'll notify you when the task resumes.");
    }
    TaskDistributor<ReviewSession> distribution = this.taskVisitorProvider
        .getTaskVisitor(batchSetting.getDistributeType(), jobUser.getRole())
        .taskDistributor();
    var reviewSessions = distribution.assignTasks(job, batch, batchSetting, jobUser, ip, isTester);

    if (reviewSessions.isEmpty()) {
      return ApiResult.success(Optional.ofNullable(Collections.EMPTY_LIST));
    }
    return ApiResult.success(
        Optional.ofNullable(decorateReviewSessions(reviewSessions, batchSetting, batch, jobUser)));
  }

  private List<TakeJobResultDTO> decorateReviewSessions(
      List<ReviewSession> reviewSessions, BatchSetting batchSetting, Batch batch, JobUser jobUser) {

    Map<Long, TaskSession> taskSessionMap = this.taskSessionService
        .getTaskSessionByIdList(
            reviewSessions.stream().map(ReviewSession::getTaskSessionId).collect(toList()))
        .stream()
        .collect(toMap(TaskSession::getId, Function.identity()));

    Map<Long, Task> taskMap = this.taskService
        .getTaskByIds(reviewSessions.stream().map(ReviewSession::getTaskId).toList())
        .stream()
        .collect(toMap(Task::getId, Function.identity()));

    Map<Long, Resource> resourceMap =
        taskService.getResourceByTaskIds(taskMap.values().mapToList(Task::getResourceId)).stream()
            .collect(toMap(Resource::getId, Function.identity()));

    List<TakeJobResultDTO> res = reviewSessions.stream()
        .map(x -> {
          Task task = taskMap.get(x.getTaskId());

          TakeJobResultDTO dto = TakeJobResultDTO.builder()
              .taskSession(taskSessionMap.get(x.getTaskSessionId()))
              .reviewSession(x)
              .build();

          if (Objects.nonNull(task)) {
            dto.setResource(resourceMap.get(task.getResourceId()));
            if (task.getIsHybrid()) {

              Optional<List<HybridResourceDTO>> hybridTaskResource =
                  this.taskService.getHybridTaskResource(task.getId());
              dto.setHybridTaskResources(hybridTaskResource.orElse(null));
            }
          }

          return dto;
        })
        .collect(toList());
    var hpMode = batchSetting.getHoneyPotSettingForReviewer();

    if (batchSetting.getDistributeType().equals(BatchSetting.DistributeType.RAW)) {
      if (hpMode == BatchSetting.HpSettingType.DP_BOUND) {
        honeyPotService.createOrGetReviewHpSessionForNewType(res, batch);
      } else { // normal hp and no hp will only contain basic questions for now.
        if (hpMode == BatchSetting.HpSettingType.NORMAL) {
          honeyPotService.adaptHpReviewSession(res, batch, jobUser, false);
        }
      }
      return res;
    }

    if (hpMode.equals(BatchSetting.HpSettingType.NORMAL)
        && CollectionUtils.isNotEmpty(batchSetting.getHoneyPotReviewBatches())) {
      if (CollectionUtils.isNotEmpty(res)) {
        honeyPotService.adaptHpReviewSession(res, batch, jobUser, false);
      }
    }
    return res;
  }

  @GetMapping("/{id}/has-pending-review-jobs")
  @IsLabeler
  public ApiResult<Boolean> hasPendingReviewJobs(@PathVariable Long id) {
    var task = jobTaskService.getOngoingReviewSessionForJob(id, ControllerUtils.currentUid());
    if (task.isPresent()) {
      return ApiResult.success(true);
    }
    var jobUser = jobUserService.getJobUserByJobAndUserId(id, ControllerUtils.currentUid());
    if (jobUser.filter(j -> j.getRole() == JobUser.JobUserRole.REVIEWER).isEmpty()) {
      return ApiResult.success(false);
    }
    return ApiResult.success(
        jobTaskService.hasPendingReviewJobForJobUser(id, jobUser.get().getId()));
  }

  // TODO: 检查当前是否已经进入audit和spot状态
  @GetMapping("/{id}/take-spot-job")
  public ApiResult<Optional<TakeJobResultDTO>> takeSpotJob(@PathVariable Long id) {
    CompletableFuture<SimpleResult<SpotSession>> r = ActorUtils.askWithDefault(
        this.clusterConfiguration.getJobSessionActor(),
        new JobSessionActor.OpGetSpotTask(id, ControllerUtils.currentUid()));
    var result = r.get();
    return result
        .asApiResult()
        .map(Optional::ofNullable)
        .map(ts -> ts.map(session -> {
          var taskSession = taskSessionService.getTaskSessionById(session.getTaskSessionId());
          var reviewSession = taskSession.flatMap(
              s -> reviewSessionService.getReviewSessionByTaskSessionId(s.getId()));
          var taskOption = taskService.getTaskById(session.getTaskId());

          var resData = TakeJobResultDTO.builder()
              .taskSession(taskSession.orElse(null))
              .reviewSession(reviewSession.orElse(null))
              .spotSession(session)
              .resource(taskService.getResourceByTaskId(session.getTaskId()).orElse(null))
              .build();

          taskOption.ifPresent(task -> {
            if (task.getIsHybrid()) {
              var hybridResources = taskService.getHybridTaskResource(task.getId());
              hybridResources.ifPresent(resData::setHybridTaskResources);
            }
          });

          return resData;
        }));
  }

  @GetMapping("/{id}/spot-job-stat")
  public ApiResult<JobAmAuditStatDTO> getSpotJobStat(@PathVariable Long id) {
    jobService.validateJobPermission(
        id, ControllerUtils.currentUid(), ControllerUtils.isAccountManager());
    return taskSessionService.getSpotStatByJobId(id).toApiResult();
  }

  @GetMapping("/{id}/take-am-audit-job")
  @IsAccountManager
  public ApiResult<Optional<TakeJobResultDTO>> takeAmAuditJob(@PathVariable Long id) {
    var uid = ControllerUtils.currentUid();
    CompletableFuture<SimpleResult<TaskSession>> r = ActorUtils.askWithDefault(
        this.clusterConfiguration.getJobSessionActor(),
        new JobSessionActor.OpGetAmAuditTask(id, uid));
    var result = r.get();
    return result.asApiResult().map(ts -> Optional.ofNullable(ts)
        .map(session -> TakeJobResultDTO.builder()
            .taskSession(session)
            .resource(taskService.getResourceByTaskId(session.getTaskId()).orElse(null))
            .build()));
  }

  @PostMapping("/{id}/reset-submission")
  @Operation(summary = "clear all job submission status, developer only!")
  public ApiResult<Boolean> resetSubmissionStatus(@PathVariable Long id) {
    jobTaskService.resetJobSubmissionStatus(id);
    return ApiResult.success(true);
  }

  @PostMapping("/{id}/accept-job-nda")
  @IsLabeler
  @Operation(summary = "sign a job's nda")
  public ApiResult<Boolean> acceptJobNda(@PathVariable Long id) {
    // TODO: move to job session actor
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));

    var ju = jobUserService
        .getJobUserByJobAndUserId(id, ControllerUtils.currentUid())
        .filter(j -> !j.getDeleted())
        .orThrow(() -> ControllerUtils.forbidden("you are not allowed to accept this job"))
        .filter(j -> j.getRole().equals(NDA_SIGNING)
            || (Arrays.asList(LABELER, REVIEWER).contains(j.getRole())
                && job.getJobType().equals(INDIVIDUAL)))
        .orElseThrow(() -> ControllerUtils.forbidden("no need to sign nda"));

    var ndas = batchNDADao.getNDAsByBatch(job.getBatchId());
    jobUserService.signNda(ju, ndas, job.getJobType());
    return ApiResult.success(true);
  }

  @PostMapping("/{id}/accept-job")
  @IsLabeler
  @Operation(summary = "accept a job and start working on it")
  public ApiResult<Boolean> acceptJob(@PathVariable Long id) {
    // TODO: move to job session actor
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));

    var ju = jobUserService
        .getJobUserByJobAndUserId(id, ControllerUtils.currentUid())
        .filter(j -> !j.getDeleted())
        .orThrow(() -> ControllerUtils.forbidden("you are not allowed to accept this job"))
        .filter(j -> j.getRole().equals(JobUser.JobUserRole.INVITATION))
        .orElseThrow(() -> ControllerUtils.forbidden("already accepted"));

    var batch = batchService
        .getBatchById(job.getBatchId())
        .filter(b -> !b.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound(BATCH_NOT_FOUND));

    var examTl = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.EXAM)
        .firstOrNull()
        .asOpt()
        .orElseThrow(() -> ControllerUtils.notFound("exam task list not found"));

    jobSessionService.acceptJobAndCreateExamSessions(job, ju, batch, examTl);
    return ApiResult.success(true);
  }

  @GetMapping("/{id}/exam-sessions/unreviewed")
  @IsNodeManager
  @Operation(summary = "get exam sessions by node manager")
  public ApiResult<List<ExamSessionDetailsDTO>> getExamSessionUnreviewed(@PathVariable Long id) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));
    nodeService
        .getNodeByNodeManager(ControllerUtils.currentUid())
        .filter(b -> !b.getDeleted() && Objects.equals(b.getId(), job.getNodeId()))
        .ensurePresent(() -> ControllerUtils.notFound("no perm."));
    var firstUnReviewedExamSessionJobUser =
        jobUserService.getFirstUnReviewedJobUserExamSessions(id);
    return firstUnReviewedExamSessionJobUser
        .map(jobSessionService::getExamSessionDetails)
        .toList()
        .toApiResult();
  }

  @PostMapping("/{id}/exam-sessions/unreviewed")
  @IsNodeManager
  @Operation(summary = "update audit of exam session by node manager")
  public void setExamSessionAudit(@PathVariable Long id, @RequestBody AuditByNodeManagerDTO body) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));
    nodeService
        .getNodeByNodeManager(ControllerUtils.currentUid())
        .filter(b -> !b.getDeleted() && Objects.equals(b.getId(), job.getNodeId()))
        .ensurePresent(() -> ControllerUtils.notFound("no perm."));

    this.batchService
        .getBatchById(job.getBatchId())
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound(BATCH_NOT_FOUND))
        .filter(b -> b.getStatus().equals(Batch.BatchStatus.PRE_TASK_EXAM))
        .ensurePresent(() -> ControllerUtils.notFound("batch not in PRE_TASK_EXAM status"));

    jobSessionService.setExamSessionAudit(id, body);
  }

  @GetMapping("/{id}/exam-sessions")
  @IsLabeler
  @Operation(summary = "get exam sessions")
  public ApiResult<List<ExamSessionDetailsDTO>> getExamSessions(@PathVariable Long id) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));

    var ju = jobUserService
        .getJobUserByJobAndUserId(id, ControllerUtils.currentUid())
        .filter(j -> !j.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("you are not allowed to accept this job"));

    return jobSessionService.getExamSessionDetailsByJob(job, ju).toApiResult();
  }

  @GetMapping("/{id}/exam-stats")
  public ApiResult<JobStatDTO> getJobStats(@PathVariable Long id) {
    jobService.getJobById(id).ensurePresent(() -> ControllerUtils.notFound("job not found"));

    return jobSessionService.getJobStat(id).toApiResult();
  }

  @PostMapping("/{id}/submit-am-review")
  // @IsAccountManager
  public ApiResult<Boolean> submitAmReview(
      @PathVariable Long id,
      @RequestParam(defaultValue = "true") Boolean approve,
      @Validated @RequestBody SignatureDTO payload) {
    if (Web2AuthService.isWeb3Login() && payload.getSignature().isEmpty()) {
      throw ControllerUtils.badRequest("missing signature");
    }

    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));

    // FIXME: enforce account manager project.accountMnaagerId check
    var am = userService
        .getUserById(ControllerUtils.currentUid())
        .filter(u -> !u.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("account manager not found"));

    CompletableFuture<SimpleResult<Void>> r = ActorUtils.askWithDefault(
        this.clusterConfiguration.getJobSessionActor(),
        new JobSessionActor.OpSubmitAmReview(job.getId(), approve, payload.getSignature(), am));
    return r.get().asApiResult().map(_v -> true);
  }

  @GetMapping("/{id}/am-review-sign-message")
  @IsAccountManager
  public ApiResult<JobAmReviewSignMessageDTO> getJobAmReviewSignMessage(
      @PathVariable Long id, @RequestParam Boolean acceptance) {
    jobService.getJobById(id).ensurePresent(() -> ControllerUtils.notFound("job not found"));

    // FIXME: enforce account manager project.accountMnaagerId check
    var am = userService
        .getUserById(ControllerUtils.currentUid())
        .filter(u -> !u.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("account manager not found"));

    return jobSessionService.getAmReviewSignMessage(id, am, acceptance).toApiResult();
  }

  @GetMapping("/{id}/am-audit-stats")
  public ApiResult<JobAmAuditStatDTO> getJobAmAuditStats(@PathVariable Long id) {
    jobService.getJobById(id).ensurePresent(() -> ControllerUtils.notFound("job not found"));

    return jobSessionService.getAmAuditStat(id).toApiResult();
  }

  // @GetMapping("/{id}/take-nm-audit-job")
  // @IsNodeManager
  // public ApiResult<Optional<TakeJobResultDTO>> takeNmAuditJob(@PathVariable Long id) {
  // CompletableFuture<Messages.SimpleResult<Optional<TaskSession>>> r =
  // ActorUtils.askWithDefault(jobSessionActor,
  // new JobSessionActor.OpGetNmAuditTask(id));
  // var result = r.get();
  // return result.asApiResult().map(ts -> ts.map(session -> TakeJobResultDTO
  // .builder()
  // .taskSession(session)
  // .resource(taskService.getResourceByTaskId(session.getTaskId()).orElse(null)).build()));
  // }

  @GetMapping("/{id}/nm-audit-stats")
  public ApiResult<JobNmAuditStatDTO> getJobNmAuditStats(@PathVariable Long id) {
    jobService.getJobById(id).ensurePresent(() -> ControllerUtils.notFound("job not found"));

    return jobSessionService.getNmAuditStat(id).toApiResult();
  }

  @GetMapping("/{id}/submit-nm-sign-message")
  @IsNodeManager
  public ApiResult<NodeManagerSubmitJobSignMessageDTO> getNmSubmitJobSignMessage(
      @PathVariable Long id) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));
    var node = nodeService
        .getNodeById(job.getNodeId())
        .orElseThrow(() -> ControllerUtils.internal("node not found"));
    var nm = userService
        .getUserById(ControllerUtils.currentUid())
        .filter(u -> !u.getDeleted() && Objects.equals(u.getId(), node.getNodeManagerId()))
        .orElseThrow(() -> ControllerUtils.forbidden("node manager not found"));

    return jobSessionService.getNmSubmitJobSignMessage(job, nm).toApiResult();
  }

  @PostMapping("/{id}/submit-nm-review")
  @IsNodeManager
  public ApiResult<Boolean> submitNmReview(
      @PathVariable Long id, @RequestParam(defaultValue = "true") Boolean approve) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));

    // TODO: move to job session actor
    // TODO: safe guard

    jobService.nmReview(job, approve);
    jobService.checkAllJobStatus(job.getBatchId());
    // jobService.checkAllJobUserStatus(job.getId());

    return ApiResult.success(true);
  }

  @PostMapping("/{id}/reset-submission2")
  @Operation(summary = "clear all job submission status, developer only!")
  public ApiResult<Boolean> resetJobState(@PathVariable Long id) {
    jobSessionService.resetReviewStatus(id);
    return ApiResult.success(true);
  }

  @PostMapping("/{id}/invite-user")
  @IsNodeManager
  public ApiResult<JobUser> inviteJobUser(
      @PathVariable Long id, @RequestBody InviteJobUserDTO data) {
    // TODO: validate perm
    jobService
        .getJobById(id)
        .orThrow(() -> ControllerUtils.notFound("job not found"))
        .filter(j -> j.getNodeId() != null)
        .ensurePresent(() -> ControllerUtils.forbidden("job not assigned to a node"));
    // TODO: ensure user is no node

    jobUserService
        .getJobUserByJobAndUserId(id, data.getUserId())
        .filter(u -> !u.getDeleted())
        .ifPresent(j -> ControllerUtils.badRequest("user already invited"));

    return jobService.inviteJobUser(id, data, ControllerUtils.currentUid()).toApiResult();
  }

  @PostMapping("/{id}/submit-by-nm")
  @IsNodeManager
  public ApiResult<Job> submitJobByNm(
      @PathVariable Long id, @Validated @RequestBody SignatureDTO payload) {
    if (Web2AuthService.isWeb3Login() && payload.getSignature().isEmpty()) {
      throw ControllerUtils.badRequest("missing signature");
    }

    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));
    if (job.getStatus() == Job.JobStatus.AM_AUDIT) {
      return ApiResult.success(job);
    }

    var node = nodeService
        .getNodeById(job.getNodeId())
        .orElseThrow(() -> ControllerUtils.internal("node not found"));
    var nm = userService
        .getUserById(ControllerUtils.currentUid())
        .filter(u -> !u.getDeleted() && Objects.equals(u.getId(), node.getNodeManagerId()))
        .orElseThrow(() -> ControllerUtils.forbidden("node manager not found"));

    CompletableFuture<SimpleResult<Job>> r = ActorUtils.askWithDefault(
        this.clusterConfiguration.getJobSessionActor(),
        new JobSessionActor.OpSubmitJobByNm(id, payload.getSignature(), nm));
    return r.get().asApiResult();
  }

  @GetMapping("/{id}/submit-qm-sign-message")
  @IsQueueManager
  public ApiResult<QueueManagerSubmitJobSignMessageDTO> getQmSubmitJobSignMessage(
      @PathVariable Long id) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));
    var qm = userService
        .getUserById(ControllerUtils.currentUid())
        .filter(u -> !u.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("queue manager not found"));

    return jobSessionService.getQmubmitJobSignMessage(job, qm).toApiResult();
  }

  @PostMapping("/{id}/submit-by-qm")
  @IsQueueManager
  public ApiResult<Job> submitJobByQm(
      @PathVariable Long id, @Validated @RequestBody SignatureDTO payload) {
    if (Web2AuthService.isWeb3Login() && payload.getSignature().isEmpty()) {
      throw ControllerUtils.badRequest("missing signature");
    }

    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));
    if (job.getStatus() == Job.JobStatus.AM_AUDIT) {
      return job.toApiResult();
    }

    var qm = userService
        .getUserById(ControllerUtils.currentUid())
        .filter(u -> !u.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("queue manager not found"));

    CompletableFuture<SimpleResult<Job>> r = ActorUtils.askWithDefault(
        this.clusterConfiguration.getJobSessionActor(),
        new JobSessionActor.OpSubmitJobByQm(id, payload.getSignature(), qm));
    return r.get().asApiResult();
  }

  @GetMapping("/{jobId}/get-annotations-summary")
  @IsNodeManager
  public ApiResult<PageResult<TaskAuditForNodeManagerDTO>> getAnnotationsSummary(
      @PathVariable Long jobId,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam(defaultValue = "AllSubmission") AuditSubmissionType source) {
    return jobService
        .getAnnotationsSummary(jobId, ControllerUtils.currentUid(), Page.of(page, limit), source)
        .toPageResult()
        .toApiResult();
  }

  @GetMapping("/{id}/stat-for-labeler/{labelerId}")
  public ApiResult<JobUserStatDTO> getLabelerStat(
      @PathVariable Long id, @PathVariable Long labelerId) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));
    return taskSessionService.getLabelerStat(job, labelerId).toApiResult();
  }

  @PostMapping("/{id}/reject-labeler/{labelerId}")
  public ApiResult<Boolean> rejectLabeler(
      @PathVariable Long id,
      @PathVariable Long labelerId,
      @RequestBody SimpleDataDTO<String> data) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));

    // TODO: validate current user is a reviewer?
    var ju = jobUserService
        .getJobUserByJobAndUserId(id, labelerId)
        .filter(j -> !j.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("job user not found"))
        .filter(j -> j.getRole() == LABELER)
        .orElseThrow(() -> ControllerUtils.notFound("labeler not found"));

    // TODO: validate labeler submission rate?
    CompletableFuture<SimpleResult<Boolean>> r = ActorUtils.askWithDefault(
        this.clusterConfiguration.getJobSessionActor(),
        new JobSessionActor.OpRejectLabeler(
            ju, ControllerUtils.currentUid(), data.getData() == null ? "" : data.getData()));
    var resp = r.get().asApiResult();

    var meme = data.getData() == null ? "" : data.getData();
    notificationService.noticeNMLabelerJobSentBack(
        ControllerUtils.currentUid(), job.getId(), labelerId, meme);
    notificationService.noticeLabelerJobSentBackByReviewer(
        ControllerUtils.currentUid(), job.getId(), labelerId, meme);

    return resp;
  }

  @GetMapping("/{id}/stat-for-reviewer/{reviewerId}")
  public ApiResult<JobUserStatDTO> getReviewerStat(
      @PathVariable Long id, @PathVariable Long reviewerId) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));
    return reviewSessionService.getReviewerStat(job, reviewerId).toApiResult();
  }

  @PostMapping("/{id}/reject-reviewer/{reviewerId}")
  public ApiResult<Boolean> rejectReviewer(
      @PathVariable Long id,
      @PathVariable Long reviewerId,
      @RequestBody SimpleDataDTO<String> data) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));

    // TODO: validate current user is a reviewer?
    var ju = jobUserService
        .getJobUserByJobAndUserId(id, reviewerId)
        .filter(j -> !j.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("job user not found"))
        .filter(j -> j.getRole() == REVIEWER)
        .orElseThrow(() -> ControllerUtils.notFound("reviewer not found"));

    // TODO: validate labeler submission rate?
    CompletableFuture<SimpleResult<Boolean>> r = ActorUtils.askWithDefault(
        this.clusterConfiguration.getJobSessionActor(),
        new JobSessionActor.OpRejectReviewer(
            ju, ControllerUtils.currentUid(), data.getData() == null ? "" : data.getData()));
    var resp = r.get().asApiResult();

    var meme = data.getData() == null ? "" : data.getData();
    notificationService.noticeNMReviewerJobSentBack(
        ControllerUtils.currentUid(), job.getId(), reviewerId, meme);
    notificationService.noticeReviewerJobSentBackBySpotter(
        ControllerUtils.currentUid(), job.getId(), reviewerId, meme);

    return resp;
  }

  @GetMapping("/{id}/stat-for-spotter/{spotterId}")
  public ApiResult<JobUserStatDTO> getSpotterStat(
      @PathVariable Long id, @PathVariable Long spotterId) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));
    return spotSessionService.getSpotterStat(job, spotterId).toApiResult();
  }

  @PostMapping("/{id}/reject-user/{uid}")
  @IsNodeManager
  public ApiResult<Boolean> rejectUser(
      @PathVariable Long id, @PathVariable Long uid, @RequestBody SimpleDataDTO<String> data) {
    jobService.getJobById(id).ensurePresent(() -> ControllerUtils.notFound("job not found"));

    // TODO: validate current user is a reviewer?
    var ju = jobUserService
        .getJobUserByJobAndUserId(id, uid)
        .filter(j -> !j.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("job user not found"))
        .filter(j -> j.getRole() != ADMIN)
        .orElseThrow(() -> ControllerUtils.forbidden("admin user can't be removed"));

    CompletableFuture<SimpleResult<Boolean>> r = ActorUtils.askWithDefault(
        this.clusterConfiguration.getJobSessionActor(),
        new JobSessionActor.OpRejectUser(
            ju, ControllerUtils.currentUid(), data.getData() == null ? "" : data.getData()));

    return r.get().asApiResult();
  }

  @GetMapping("/{id}/feedbacks")
  @CheckPageParam()
  public ApiResult<PageResult<TaskSessionWithLabelerDTO>> getFeedbacks(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @PathVariable Long id) {
    return taskSessionService.pageFeedbacksByJobId(id, Page.of(page, limit)).toApiResult();
  }

  @GetMapping("/{id}/is-user-job-role-activated")
  public ApiResult<Boolean> isUserJobRoleActivated(@PathVariable Long id) {
    jobService.getJobById(id).ensurePresent(() -> ControllerUtils.notFound("job not found"));

    var jobUser = jobUserService
        .getJobUserByJobAndUserId(id, ControllerUtils.currentUid())
        .orElseThrow(() -> ControllerUtils.notFound("job use not found"));

    return jobUser.getActive().toApiResult();
  }

  @GetMapping("/{id}/user-activate-sign-message")
  public ApiResult<UserActivateJobRoleSignMessageDTO> getUserActivateJobRoleSignMessage(
      @PathVariable Long id) {
    jobService.getJobById(id).ensurePresent(() -> ControllerUtils.notFound("job not found"));

    var jobUser = jobUserService
        .getJobUserByJobAndUserId(id, ControllerUtils.currentUid())
        .orElseThrow(() -> ControllerUtils.notFound("job use not found"));

    var user = userService
        .getUserById(ControllerUtils.currentUid())
        .filter(u -> !u.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("user not found"));

    return jobUserService
        .getUserActiveJobRoleSignMessage(user, id, jobUser.getId())
        .toApiResult();
  }

  @PostMapping("/{id}/user-activate")
  public ApiResult<Boolean> userActivateJobRole(
      @PathVariable Long id, @Validated @RequestBody SignatureDTO payload) {
    var job =
        jobService.getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));

    var jobUser = jobUserService
        .getJobUserByJobAndUserId(job.getId(), ControllerUtils.currentUid())
        .orElseThrow(() -> ControllerUtils.notFound("job use not found"));
    if (jobUser.getActive()) {
      return ApiResult.success(true);
    }

    var user = userService
        .getUserById(ControllerUtils.currentUid())
        .filter(u -> !u.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("user not found"));

    CompletableFuture<SimpleResult<Boolean>> r = ActorUtils.askWithDefault(
        this.clusterConfiguration.getJobSessionActor(),
        new JobSessionActor.OpActivateUserJobRole(
            id, payload.getSignature(), user, jobUser.getId()));
    return r.get().asApiResult();
  }

  @PostMapping("/for-pre-task")
  @IsQueueManager
  public ApiResult<Boolean> createJobByPreTask(@Valid @RequestBody CreateJobByPreTaskDTO payload) {
    jobService.createJobByPreTask(payload, ControllerUtils.currentUid());
    return ApiResult.success(Boolean.TRUE);
  }

  @GetMapping("/batches/{batchId}/nodes/waiting-for-allocation")
  @IsQueueManager
  public ApiResult<List<NodeDetailsDTO>> waitingForAllocation(
      @PathVariable Long batchId,
      @RequestParam(required = false) Long nodeId,
      @RequestParam(required = false) String filterName) {

    return ApiResult.success(this.jobService.waitingForAllocation(batchId, nodeId, filterName));
  }

  @GetMapping("/v2/batches/{batchId}/nodes/waiting-for-allocation")
  @IsQueueManager
  public ApiResult<List<NodeDetailsDTO>> waitingForAllocationV2(
      @PathVariable Long batchId,
      @RequestParam(required = false) Long nodeId,
      @RequestParam(required = false) String filterName) {

    return ApiResult.success(this.jobService.waitingForAllocationV2(batchId, nodeId, filterName));
  }

  @GetMapping("/batches/{batchId}/jobs")
  @IsQueueManager
  public ApiResult<List<JobDetailsDTO>> getJobsByBatchId(
      @PathVariable(required = false) Long batchId,
      @RequestParam(required = false) String filter,
      @RequestParam(required = false) List<Job.JobStatus> status) {
    return ApiResult.success(this.jobService.getJobsByBatchIdAndStatus(batchId, filter, status));
  }

  @GetMapping("batches/{batchId}/exam-sessions")
  //  @IsAccountManager
  public ApiResult<List<ExamSessionDetailsDTO>> examSessions(
      @PathVariable Long batchId, @RequestParam Long userId) {

    return this.jobService
        .examSessions(batchId, userId)
        .map(jobSessionService::getExamSessionDetails)
        .toList()
        .toApiResult();
  }

  @PutMapping("/{jobId}/reassigning-user-role")
  @IsNodeManager
  public ApiResult<Boolean> reassigningUserRole(
      @PathVariable Long jobId, @RequestParam Long userId, JobUser.JobUserRole role) {
    this.jobUserService.reassigningUserRole(jobId, userId, role, ControllerUtils.currentUid());
    return ApiResult.success(true);
  }

  @PostMapping("/{jobId}/change-deadline")
  @IsAdmin
  public ApiResult<Boolean> changeDeadline(
      @PathVariable Long jobId,
      @RequestParam(required = false) Timestamp auditDeadline,
      @RequestParam(required = false) Timestamp reviewDeadline) {
    this.jobService.changeDeadline(jobId, auditDeadline, reviewDeadline);
    return ApiResult.success(true);
  }

  @PostMapping("/{jobId}/change-role")
  @IsAdmin
  public ApiResult<Boolean> changeDeadline(
      @PathVariable Long jobId, @RequestParam Long userId, @RequestParam JobUser.JobUserRole role) {
    this.jobUserService.changeRole(jobId, userId, role);
    return ApiResult.success(true);
  }

  @GetMapping("/{jobId}/query-job-users")
  @IsAdmin
  @CheckPageParam()
  public ApiResult<PageResult<JobUserWithUserDTO>> queryJobUsers(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer size,
      @PathVariable Long jobId,
      @RequestParam Optional<JobUser.JobUserRole> role,
      @RequestParam(defaultValue = "true") Boolean deleted,
      @RequestParam(defaultValue = "id") JobFields sortBy,
      @RequestParam(defaultValue = "false") Boolean desc) {
    var jobUserPage = jobUserService.queryJobUsers(page, size, jobId, role, deleted, sortBy, desc);
    var dtos = jobUserPage
        .getRecords()
        .map(ju -> JobUserWithUserDTO.builder()
            .jobUser(ju)
            .user(userService
                .getUserById(ju.getUserId())
                .orElseThrow(() -> ControllerUtils.forbidden("user not found")))
            .build())
        .toList();

    return ApiResult.success(PageResult.<JobUserWithUserDTO>builder()
        .pages(jobUserPage.getPages())
        .size(jobUserPage.getSize())
        .total(jobUserPage.getTotal())
        .data(dtos)
        .build());
  }

  @PostMapping("/{jobId}/users/{userId}/recalculate-counters")
  @IsAdmin
  public ApiResult<Boolean> recalculateCounters(
      @PathVariable Long jobId, @PathVariable Long userId) {
    jobUserService.recalculateCounters(jobId, userId);
    return ApiResult.success(true);
  }
}
