package ai.saharaa.controllers;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.reward.*;
import ai.saharaa.enums.SortType;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.newTasks.RewardTokenInfo;
import ai.saharaa.model.newTasks.UserTokenRewardClaims;
import ai.saharaa.model.newTasks.UserTokenTaskRewards;
import ai.saharaa.services.JobUserService;
import ai.saharaa.services.newTasks.NewRewardService;
import ai.saharaa.services.newTasks.WithdrawalService;
import ai.saharaa.utils.ControllerUtils;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.List;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/rewards")
@RequiredArgsConstructor
@Slf4j
public class RewardController {

  private final NewRewardService newRewardService;

  private final WithdrawalService withdrawalService;
  private final JobUserService jobUserService;

  @GetMapping("/my-earnings")
  public ApiResult<MyEarningsDTO> getMyEarnings() {
    return ApiResult.success(newRewardService.getMyEarnings(ControllerUtils.currentUid()));
  }

  @GetMapping("/earning-breakdown")
  @CheckPageParam
  public ApiResult<PageResult<EarningBreakdownDTO>> getEarningBreakdown(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer size,
      @RequestParam(required = false) Integer coinType,
      @RequestParam(defaultValue = "") String taskName,
      @RequestParam(defaultValue = "false") Boolean claimed,
      @RequestParam(defaultValue = "createdAtDesc") SortType sortType,
      @RequestParam(defaultValue = "") String role) {
    return ApiResult.success(newRewardService
        .getEarningBreakdown(
            ControllerUtils.currentUid(), page, size, coinType, taskName, role, sortType, claimed)
        .toPageResult());
  }

  @GetMapping("/request-claim")
  public ApiResult<List<UserTokenTaskRewards>> requestClaim(@RequestParam Integer tokenType) {
    return ApiResult.success(
        newRewardService.requestClaim(ControllerUtils.currentUid(), tokenType));
  }

  @GetMapping("/request-claim/check-after")
  public ApiResult<List<UserTokenTaskRewards>> requestClaimCheckAfter(
      @RequestParam Integer tokenType) {
    return ApiResult.success(
        newRewardService.requestClaimCheckAfter(ControllerUtils.currentUid(), tokenType));
  }

  @GetMapping("/token-info")
  public ApiResult<RewardTokenInfo> getTokenInfo(@RequestParam Integer tokenType) {
    return ApiResult.success(newRewardService.getTokenInfo(tokenType));
  }
  // New withdrawal endpoints
  @PostMapping("/request-withdrawal")
  public ApiResult<UserTokenRewardClaims> requestWithdrawal(
      @RequestBody @Valid WithdrawalRequestDTO withdrawalRequest) {
    UserTokenRewardClaims withdrawalRecord =
        withdrawalService.requestWithdrawal(ControllerUtils.currentUid(), withdrawalRequest);
    return ApiResult.success(withdrawalRecord);
  }

  @GetMapping("/withdrawal-history")
  @CheckPageParam
  public ApiResult<PageResult<WithdrawalHistoryDTO>> getWithdrawalHistory(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer size,
      @RequestParam(required = false) SortType sortBy,
      @RequestParam(required = false) Boolean inProgress) {
    return ApiResult.success(withdrawalService
        .getWithdrawalHistory(ControllerUtils.currentUid(), page, size, sortBy, inProgress)
        .toPageResult());
  }

  @GetMapping("/get-claim-parameters/{withdrawalId}")
  public ApiResult<ClaimParametersDTO> getClaimParameters(@PathVariable Long withdrawalId) {
    ClaimParametersDTO params =
        withdrawalService.getClaimParameters(ControllerUtils.currentUid(), withdrawalId);
    return ApiResult.success(params);
  }

  @PostMapping("/confirm-claim/{withdrawalId}")
  public ApiResult<Void> confirmClaim(
      @PathVariable Long withdrawalId, @RequestBody ConfirmationDTO confirmation) {
    withdrawalService.confirmClaimSuccess(
        ControllerUtils.currentUid(), withdrawalId, confirmation.getTxHash());
    return ApiResult.success(null);
  }

  @PostMapping("/finalize-job/{jobId}")
  public ResponseEntity<String> finalizeJob(@PathVariable Long jobId) {
    try {
      jobUserService.finalizeJobOnChain(jobId);
      return ResponseEntity.ok(
          "Finalization process for job " + jobId + " has been triggered successfully.");
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body("Failed to finalize job: " + e.getMessage());
    }
  }

  @PostMapping("/{batchId}/task_status")
  public ResponseEntity<String> updateTaskStatus(
      @PathVariable Long batchId, @RequestParam Integer taskStatus) {
    try {
      jobUserService.updateTaskStatus(batchId, taskStatus);
      return ResponseEntity.ok("ok");
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body("Failed to updateTaskStatus job: " + e.getMessage());
    }
  }

  // Helper DTO for the confirmation endpoint
  @Data
  public static class ConfirmationDTO {
    @NotBlank
    private String txHash;
  }
}
