spring.application.name=campfire-platform
spring.main.banner-mode=off
logging.include-application-name=false
logging.level.org.springframework=ERROR
logging.file.name=./logs/campfire-platform-logger.log
# TODO consider removing the following config and let Spring to use logging.file.name to figure it out
logging.logback.rollingpolicy.file-name-pattern=./logs/archived/campfire-platform-logger-%d{yyyy-MM-dd}.%i.log
logging.logback.rollingpolicy.total-size-cap=1GB
logging.structured.format.console=ecs
logging.structured.format.file=ecs
# Enable micrometer annotations such as @Timed so individual metrics can be pushed to Prometheus
management.observations.annotations.enabled=true
spring.jpa.hibernate.ddl-auto=none
# Tracing configurations https://docs.spring.io/spring-boot/reference/actuator/tracing.html
# Without this customization, the default tracing correlation will produce a lot of empty spaces.
# The default only pushes 10% of traces to Zipkin. Push 100% instead.
management.tracing.sampling.probability=1
# Connect using simple url and credentials. For the future, a more secure way to connect to Cloud SQL from GKE is to use
# Cloud SQL Auth Proxy. https://cloud.google.com/sql/docs/postgres/connect-kubernetes-engine
spring.datasource.primary.jdbc-url=${AI_DATABASE:***********************************}
spring.datasource.primary.username=${AI_DB_USER:root}
spring.datasource.primary.password=${AI_DB_PASS:123456}
spring.datasource.primary.driver-class-name=org.postgresql.Driver
spring.datasource.hikari.maximum-pool-size=${AI_DB_POOL_SIZE:50}
spring.datasource.onchain.jdbc-url=${DATABASE_ONCHAIN:************************************************}
spring.datasource.onchain.schema=${DB_SCHEMA_ONCHAIN:sgd62}
spring.datasource.onchain.username=${DB_USER_ONCHAIN:graph-node}
spring.datasource.onchain.password=${DB_PASS_ONCHAIN:let-me-in}
spring.datasource.onchain.driver-class-name=org.postgresql.Driver
spring.data.redis.host=${AI_REDIS_HOST:127.0.0.1}
spring.data.redis.database=0
spring.data.redis.port=${AI_REDIS_PORT:6379}
spring.data.redis.password=${AI_REDIS_PASSWORD:123456}
spring.data.redis.ssl.enabled=${AI_REDIS_SSL_ENABLED:false}
cache.default.db.index=0
cache.api.db.index=1
cache.rateLimit.db.index=2
cache.taskQueue.db.index=15
cache.workload.db.index=14
management.metrics.enable.hikari=true
# Spring's support of otlp grpc is being added
# https://github.com/spring-projects/spring-boot/issues/41460
management.otlp.tracing.endpoint=http://tempo:4318/v1/traces
management.tracing.enabled=false
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
akka.cluster.name=${AI_CLUSTER_NAME:ai-dev-cluster}
spring.flyway.url=${AI_DATABASE:***********************************}
spring.flyway.user=${AI_DB_USER:root}
spring.flyway.password=${AI_DB_PASS:123456}
spring.flyway.validate-migration-naming=true
management.otlp.metrics.export.enabled=true
management.otlp.metrics.export.step=10s
authentication.url=${AUTHENTICATION_URL:https://dev.authentication.saharaa.info}
management.otlp.metrics.export.url=${OTEL_EXPORTER_OTLP_HTTP_ENDPOINT:http://localhost:4317}
internal.api.key=${INTERNAL_API_KEY:xxxx}
spring.flyway.baseline-on-migrate=true
spring.servlet.multipart.max-file-size=1024MB
spring.servlet.multipart.max-request-size=1024MB
spring.devtools.remote.restart.enabled=false
# this prevents error for deserializing into a simple @Data class with a single field.
spring.jackson.constructor-detector=use_properties_based
ai.saharaa.jwt.secret=${AI_JWT_SECRET:ai-secret}
# in seconds, default 7 days
ai.saharaa.jwt.ttl=${AI_JWT_TTL:604800}
# in seconds, default 3 days
ai.saharaa.jwt.refreshTTL=${AI_JWT_REFRESH_TTL:259200}
ai.saharaa.data.url=${AI_DATA_URL:https://data-stage.saharaa.ai}
ai.saharaa.cloud.provider=${CLOUD_PROVIDER:aws}
server.shutdown=graceful
spring.lifecycle.timeout-per-shutdown-phase=20s
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
resource.base-path=${AI_STORE_PATH:/opt/ai-store}
web3auth.token-expire=${AI_WEB3AUTH_TOKEN_EXPIRE:259200}
aws.url_expires=${AI_AWS_URL_EXPIRES:3600}
aws.s3.enable=${AWS_S3_ENABLE:false}
aws.s3.region=${AWS_S3_REGION:}
aws.s3.bucket=${AWS_S3_BUCKET:}
aws.s3.id=${AWS_S3_ACCOUNT_ID:}
aws.s3.access_key_id=${AWS_S3_ACCESS_KEY_ID:}
aws.s3.secret_access_key=${AWS_S3_SECRET_ACCESS_KEY:}
aws.s3.url=${AWS_S3_URL:}
aws.sts.region=${AWS_STS_REGION:}
aws.sts.bucket=${AWS_STS_BUCKET:}
aws.sts.access_key_id=${AWS_STS_ACCESS_KEY_ID:}
aws.sts.secret_access_key=${AWS_STS_SECRET_ACCESS_KEY:}
aws.ses.region=${AWS_SES_REGION:us-west-2}
aws.ses.access_key_id=${AWS_SES_ACCESS_KEY_ID:xx}
aws.ses.secret_access_key=${AWS_SES_SECRET_ACCESS_KEY:xx}
aws.sts.assume_role_session=${AWS_STS_ASSUME_ROLE_SESSION:SESSION}
aws.sts.assume_role_duration=${AWS_STS_ASSUME_ROLE_DURATION:3600}
gcs.bucket=${GCS_BUCKET:}
gke.service_account.credentials_path=${SERVICE_ACCOUNT_CREDENTIALS_PATH:}
ai.saharaa.platform.env=${AI_PLATFORM_ENV:dev}
ai.saharaa.akka.mode=${AI_AKKA_MODE:local}
ai.saharaa.web2.auth_enabled=${AI_WEB2_AUTH_ENABLED:false}
# in seconds, default 5 minutes
ai.saharaa.web2.email_code_ttl=${AI_WEB2_EMAIL_CODE_TTL:300}
ai.saharaa.web2.email_sender_address=${AI_WEB2_EMAIL_SENDER_ADDRESS:<EMAIL>}
ai.saharaa.web2.email_reset_password_url=${AI_WEB2_EMAIL_RESET_PASSWORD_URL:https://saharalabs.ai/#/newPassword?token=%s}
# in seconds, default 1 minutes
ai.saharaa.web2.email_send_to_same_address_interval=${AI_WEB2_EMAIL_SEND_TO_SAME_ADDRESS_INTERVAL:60}
ai.saharaa.web3.chain_id=${AI_WEB3_CHAIN_ID:534351}
ai.saharaa.web3.verifying_contract=${AI_WEB3_VERIFYING_CONTRACT:0xed543e231F15666d528a5d85166e92c4a85370Ca}
ai.saharaa.web3.rpc_url=${AI_WEB3_RPC_URL:https://testnet.saharalabs.ai}
ai.saharaa.web3.credentials_pk=${AI_WEB3_CREDENTIALS_PK:0x1111111111111111111111111111111111111111111111111111111111111111}
ai.saharaa.web3.achievement_manager_contract_address=${AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS:0x265EED7bF4387a5d1816850f7174cC6469ed9018}
ai.saharaa.web3.achievement_on_contract_address=${AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS:0xD9348667cef8c0bcaE5186db9478FC6e55Fa8946}
ai.saharaa.achievement_progress_onchain_batch_size=${ACHIEVEMENT_PROGRESS_ONCHAIN_BATCH_SIZE:10}
ai.saharaa.achievement_oracle_disabled=${ACHIEVEMENT_ORACLE_DISABLED:false}
ai.saharaa.site_url=${SITE_URL:https://saharalabs.ai}
ai.saharaa.statsig.server_secret=${STATSIG_SERVER_SECRET:secret-fZ4zWGI8pYjNcNDsHesGgBz9pyDk8VMUW1B9oimYYy5}
openai.key=${OPEN_AI_KEY:}
captcha.secret=${CAPTCHA_SECRET_CF:}
captcha.filter.threshold=${CAPTCHA_FILTER_THRESHOLD:11}
captcha.google.v3.secret=${GOOGLE_RECAPTCHA_SECRET_V3:}
captcha.google.v2.secret=${GOOGLE_RECAPTCHA_SECRET_V2:}
captcha.google.v3.endpoint=${GOOGLE_RECAPTCHA_V3_ENDPOINT:https://www.google.com/recaptcha/api/siteverify}
oci.user=${OCI_USER:}
oci.enable=${OCI_ENABLE:true}
oci.fingerprint=${OCI_FINGERPRINT:}
oci.tenancy=${OCI_TENANCY:}
oci.region=${OCI_REGION:}
oci.privateKey=${OCI_PRIVATE_KEY:}
oci.passphrase=${OCI_PASSPHRASE:}
oci.namespace=${OCI_NAMESPACE:}
oci.bucket=${OCI_BUCKET:}
esapi.validator.body_size_limit=${ESAPI_BODY_SIZE_LIMIT:2000000}
custom.user.address.whitelist=${USER_ADDRESS_WHITELIST_OPEN:false}
custom.user.olympic.login=${USER_NEED_OLYMPIC_LOGIN:true}
management.endpoint.metrics.enabled=true
aws.accessKeyId=${AWS_ACCESS_KEY_ID:}
aws.secretAccessKey=${AWS_SECRTE_ACCESS_KEY:}
management.endpoints.web.exposure.include=prometheus,heapdump
campfire.machine.review.url=${CAMPFIRE_MACHINE_REVIEW_URL:http://**************:8888}
campfire.machine.review.apikey=${CAMPFIRE_MACHINE_REVIEW_KEY:huxgyv-7negdY-rusdij}
#logbook.write.max-body-size=10000
#logbook.minimum-status=200
#logging.level.org.zalando.logbook=TRACE
#logbook.predicate.exclude[0].path=/api/ping/**
#logbook.predicate.exclude[1].path=/actuator/**
# Turn off the Spring Boot Whitelabel Error Page which gives out too much information through stacktrace
server.error.whitelabel.enabled=false
kafka.bootstrap-servers=${KAFKA_SERVERS:localhost:9092}
kafka.sasl.enabled=${KAFKA_SASL_ENABLE:false}
kafka.sasl.password=${KAFKA_SASL_PASSWORD:REPLACE_ME}
kafka.sasl.username=${KAFKA_SASL_USERNAME:REPLACE_ME}
workload.limits.platform.ip.daily=${WORKLOAD_LIMIT_PLATFORM_IP_DAILY:99999999}
workload.limits.platform.user.daily=${WORKLOAD_LIMIT_PLATFORM_USER_DAILY:25}
workload.limits.platform.tester.daily.ratio=${WORKLOAD_LIMIT_PLATFORM_TESTER_DAILY_RATIO:2}
onchain.url.graphql=${ONCHAIN_GRAPHQL_ENDPOINT:https://graph.saharaa.info/subgraphs/name/DataServiceMar14}

# Binance API hostname configuration
binance.api.hostname=${BINANCE_API_HOSTNAME:}

# Binance API proxy configuration
ai.saharaa.web3.dsp_reward_credentials_pk=${AI_WEB3_DSP_REWARD_CREDENTIALS_PK:0x1111111111111111111111111111111111111111111111111111111111111111}
ai.saharaa.web3.dsp_reward_contract_address=${AI_WEB3_DSP_REWARD_CONTRACT_ADDRESS:0x57F4Bc382d39fE0F9aD3D91d4B33A23Fae234445}
ai.saharaa.web3.dsp_reward_chain_id=${AI_WEB3_DSP_REWARD_CHAIN_ID:313313}
ai.saharaa.web3.dsp_reward_graphql=${AI_WEB3_DSP_REWARD_GRAPHQL_URL:http://**************:8000/subgraphs/name/DSPRewardSahara}
