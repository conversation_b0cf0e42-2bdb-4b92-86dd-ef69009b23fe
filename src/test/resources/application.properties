spring.data.redis.host=127.0.0.1
spring.data.redis.port=16370
spring.data.redis.password=
spring.data.redis.database=0
spring.data.redis.ssl.enabled=${AI_REDIS_SSL_ENABLED:false}
cache.default.db.index=0
cache.api.db.index=1
cache.rateLimit.db.index=2
cache.taskQueue.db.index=15
cache.workload.db.index=14
# datasource-micrometer-spring-boot is current not compatible with embedded-database-spring-test
# https://github.com/zonkyio/embedded-database-spring-test/issues/281
jdbc.datasource-proxy.enabled=false
management.otlp.tracing.export.enabled=false
spring.main.banner-mode=off
logging.pattern.correlation=[%X{traceId:-},%X{spanId:-}] 
management.tracing.sampling.probability=1
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
akka.cluster.name=ai-test-cluster
spring.flyway.validate-migration-naming=true
spring.flyway.baseline-on-migrate=true
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=200MB
spring.devtools.remote.restart.enabled=false
ai.saharaa.jwt.secret=test-secret
# in seconds, default 7 days
ai.saharaa.jwt.ttl=${AI_JWT_TTL:604800}
# in seconds, default 3 days
ai.saharaa.jwt.refreshTTL=${AI_JWT_REFRESH_TTL:259200}
ai.saharaa.data.url=${AI_DATA_URL:https://data-stage.saharaa.ai}
ai.saharaa.cloud.provider=gcp
server.shutdown=graceful
spring.lifecycle.timeout-per-shutdown-phase=20s
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
resource.base-path=${AI_STORE_PATH:/opt/ai-store}
web3auth.token-expire=${AI_WEB3AUTH_TOKEN_EXPIRE:259200}
aws.url_expires=${AI_AWS_URL_EXPIRES:3600}
aws.s3.enable=${AWS_S3_ENABLE:false}
aws.s3.region=${AWS_S3_REGION:}
aws.s3.bucket=${AWS_S3_BUCKET:}
aws.s3.id=${AWS_S3_ACCOUNT_ID:}
aws.s3.access_key_id=${AWS_S3_ACCESS_KEY_ID:}
aws.s3.secret_access_key=${AWS_S3_SECRET_ACCESS_KEY:}
aws.s3.url=${AWS_S3_URL:}
gcs.bucket=${GCS_BUCKET:}
authentication.url=${AUTHENTICATION_URL:https://dev.authentication.saharaa.info}
gke.service_account.credentials_path=${SERVICE_ACCOUNT_CREDENTIALS_PATH:}
aws.sts.region=${AWS_STS_REGION:us-west}
aws.sts.bucket=${AWS_STS_BUCKET:}
aws.sts.access_key_id=${AWS_STS_ACCESS_KEY_ID:}
internal.api.key=${INTERNAL_API_KEY:xxxx}
aws.sts.secret_access_key=${AWS_STS_SECRET_ACCESS_KEY:}
aws.ses.region=${AWS_SES_REGION:us-west}
aws.ses.access_key_id=${AWS_SES_ACCESS_KEY_ID:}
aws.ses.secret_access_key=${AWS_SES_SECRET_ACCESS_KEY:}
aws.sts.assume_role_session=${AWS_STS_ASSUME_ROLE_SESSION:SESSION}
aws.sts.assume_role_duration=${AWS_STS_ASSUME_ROLE_DURATION:3600}
ai.saharaa.platform.env=testing
ai.saharaa.akka.mode=${AI_AKKA_MODE:test}
ai.saharaa.web2.auth_enabled=${AI_WEB2_AUTH_ENABLED:false}
# in seconds, default 5 minutes
ai.saharaa.web2.email_code_ttl=${AI_WEB2_EMAIL_CODE_TTL:300}
ai.saharaa.web2.email_sender_address=${AI_WEB2_EMAIL_SENDER_ADDRESS:<EMAIL>}
ai.saharaa.web2.email_reset_password_url=${AI_WEB2_EMAIL_RESET_PASSWORD_URL:https://saharaa.ai/#/newPassword?token=%s}
# in seconds, default 1 minutes
ai.saharaa.web2.email_send_to_same_address_interval=${AI_WEB2_EMAIL_SEND_TO_SAME_ADDRESS_INTERVAL:60}
ai.saharaa.web3.chain_id=${AI_WEB3_CHAIN_ID:534351}
ai.saharaa.web3.verifying_contract=${AI_WEB3_VERIFYING_CONTRACT:0xed543e231F15666d528a5d85166e92c4a85370Ca}
ai.saharaa.web3.rpc_url=${AI_WEB3_RPC_URL:https://testnet.saharalabs.ai}
ai.saharaa.web3.credentials_pk=${AI_WEB3_CREDENTIALS_PK:0x1111111111111111111111111111111111111111111111111111111111111111}
ai.saharaa.web3.achievement_manager_contract_address=${AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS:0x265EED7bF4387a5d1816850f7174cC6469ed9018}
ai.saharaa.web3.achievement_on_contract_address=${AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS:0xD9348667cef8c0bcaE5186db9478FC6e55Fa8946}
ai.saharaa.achievement_progress_onchain_batch_size=${ACHIEVEMENT_PROGRESS_ONCHAIN_BATCH_SIZE:10}
ai.saharaa.achievement_oracle_disabled=${ACHIEVEMENT_ORACLE_DISABLED:false}
ai.saharaa.site_url=${SITE_URL:https://saharaa.ai}
ai.saharaa.statsig.server_secret=secret-statsig-test
ai.saharaa.statsig.local_mode=true
openai.key=${OPEN_AI_KEY:}
captcha.secret=${CAPTCHA_SECRET_CF:}
captcha.filter.threshold=${CAPTCHA_FILTER_THRESHOLD:11}
captcha.google.v3.secret=${GOOGLE_RECAPTCHA_SECRET_V3:0X12312312}
captcha.google.v3.endpoint=https://www.google.com/recaptcha/api/siteverify
esapi.validator.body_size_limit=${ESAPI_BODY_SIZE_LIMIT:2000000}
custom.user.address.whitelist=${USER_ADDRESS_WHITELIST_OPEN:false}
management.endpoint.metrics.enabled=true
management.endpoints.web.exposure.include=prometheus
oci.namespace=${OCI_NAMESPACE:}
oci.bucket=${OCI_BUCKET:}
custom.user.olympic.login=${USER_NEED_OLYMPIC_LOGIN:false}
zonky.test.database.provider=zonky
zonky.test.database.refresh=after_class
campfire.machine.review.url=${CAMPFIRE_MACHINE_REVIEW_URL:http://localhost:18888}
campfire.machine.review.apikey=${CAMPFIRE_MACHINE_REVIEW_KEY:huxgyv-7negdY-rusdij}
spring.jackson.constructor-detector=use_properties_based
aws.accessKeyId=${AWS_ACCESS_KEY_ID:}
aws.secretAccessKey=${AWS_SECRTE_ACCESS_KEY:}
kafka.bootstrap-servers=${KAFKA_SERVERS:localhost:9092}
kafka.sasl.enabled=${KAFKA_SASL_ENABLE:false}
kafka.sasl.password=${KAFKA_SASL_PASSWORD:REPLACE_ME}
kafka.sasl.username=${KAFKA_SASL_USERNAME:REPLACE_ME}
workload.limits.platform.ip.daily=${WORKLOAD_LIMIT_PLATFORM_IP_DAILY:75}
workload.limits.platform.user.daily=${WORKLOAD_LIMIT_PLATFORM_USER_DAILY:25}
workload.limits.platform.tester.daily.ratio=${WORKLOAD_LIMIT_PLATFORM_TESTER_DAILY_RATIO:2}
onchain.url.graphql=${ONCHAIN_GRAPHQL_ENDPOINT:https://graph.saharaa.info/subgraphs/name/DataServiceMar14}
ai.saharaa.web3.dsp_reward_credentials_pk=${AI_WEB3_DSP_REWARD_CREDENTIALS_PK:0x1111111111111111111111111111111111111111111111111111111111111111}
ai.saharaa.web3.dsp_reward_contract_address=${AI_WEB3_DSP_REWARD_CONTRACT_ADDRESS:0x57F4Bc382d39fE0F9aD3D91d4B33A23Fae234445}
ai.saharaa.web3.dsp_reward_chain_id=${AI_WEB3_DSP_REWARD_CHAIN_ID:313313}
ai.saharaa.web3.dsp_reward_graphql=${AI_WEB3_DSP_REWARD_GRAPHQL_URL:http://**************:8000/subgraphs/name/DSPRewardSahara}
